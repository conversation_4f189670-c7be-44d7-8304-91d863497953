#!/usr/bin/env python3
"""
Create advanced and unique energy effect images for NFT generation
"""

from PIL import Image, ImageDraw, ImageFilter, ImageEnhance
import os
import random
import math
import numpy as np

def create_holographic_field(size=(1000, 1000)):
    """Create holographic shimmer effect"""
    img = Image.new('RGBA', size, (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Create holographic lines
    for i in range(0, size[1], 8):
        # Alternating colors for holographic effect
        colors = [(0, 255, 255, 60), (255, 0, 255, 60), (255, 255, 0, 60)]
        color = colors[i % 3]
        
        # Create wavy lines
        points = []
        for x in range(0, size[0], 10):
            y = i + math.sin(x * 0.02) * 15
            points.append((x, int(y)))
        
        if len(points) > 1:
            for j in range(len(points) - 1):
                draw.line([points[j], points[j + 1]], fill=color, width=3)
    
    img = img.filter(ImageFilter.GaussianBlur(radius=1))
    return img

def create_quantum_distortion(size=(1000, 1000)):
    """Create quantum reality distortion effect"""
    img = Image.new('RGBA', size, (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    center_x, center_y = size[0] // 2, size[1] // 2
    
    # Create quantum ripples
    for ring in range(5, 200, 25):
        # Quantum uncertainty - random gaps in rings
        for angle in range(0, 360, 5):
            if random.random() > 0.3:  # 70% chance to draw
                rad = math.radians(angle)
                
                # Distortion effect
                distortion = math.sin(angle * 0.1) * 20
                x1 = center_x + math.cos(rad) * (ring + distortion)
                y1 = center_y + math.sin(rad) * (ring + distortion)
                x2 = center_x + math.cos(rad) * (ring + distortion + 10)
                y2 = center_y + math.sin(rad) * (ring + distortion + 10)
                
                # Color shifts based on position
                color_shift = int(angle / 2) % 255
                color = (color_shift, 255 - color_shift, 128, 80)
                
                draw.line([(x1, y1), (x2, y2)], fill=color, width=2)
    
    img = img.filter(ImageFilter.GaussianBlur(radius=3))
    return img

def create_digital_matrix(size=(1000, 1000)):
    """Create Matrix-style digital rain effect"""
    img = Image.new('RGBA', size, (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Digital characters (simplified as rectangles)
    for x in range(0, size[0], 20):
        column_height = random.randint(100, 400)
        start_y = random.randint(0, size[1] - column_height)
        
        for y in range(start_y, start_y + column_height, 15):
            if random.random() > 0.4:  # 60% chance to draw character
                # Fade effect - brighter at top
                fade_factor = 1 - (y - start_y) / column_height
                alpha = int(120 * fade_factor)
                
                # Green matrix color with slight variations
                green_val = int(255 * fade_factor)
                color = (0, green_val, 50, alpha)
                
                # Draw "character" as small rectangle
                char_size = random.randint(3, 8)
                draw.rectangle([
                    x, y, x + char_size, y + char_size
                ], fill=color)
    
    return img

def create_fire_aura(size=(1000, 1000)):
    """Create realistic fire aura effect"""
    img = Image.new('RGBA', size, (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    center_x, center_y = size[0] // 2, size[1] // 2
    
    # Create flame particles
    for _ in range(200):
        # Random position around center
        angle = random.uniform(0, 2 * math.pi)
        distance = random.uniform(50, 300)
        
        x = center_x + math.cos(angle) * distance
        y = center_y + math.sin(angle) * distance
        
        # Flame colors - red to yellow gradient
        heat = random.uniform(0, 1)
        if heat < 0.3:
            color = (255, int(100 + heat * 155), 0, random.randint(60, 120))  # Red
        elif heat < 0.7:
            color = (255, int(150 + heat * 105), int(heat * 100), random.randint(80, 140))  # Orange
        else:
            color = (255, 255, int(200 + heat * 55), random.randint(100, 160))  # Yellow
        
        # Flame particle size
        particle_size = random.randint(5, 20)
        
        draw.ellipse([
            x - particle_size, y - particle_size,
            x + particle_size, y + particle_size
        ], fill=color)
    
    img = img.filter(ImageFilter.GaussianBlur(radius=8))
    return img

def create_ice_crystal(size=(1000, 1000)):
    """Create ice crystal formation effect"""
    img = Image.new('RGBA', size, (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    center_x, center_y = size[0] // 2, size[1] // 2
    
    # Create ice crystal patterns
    for crystal in range(8):
        angle = crystal * 45  # 8 main directions
        
        for length in range(50, 300, 20):
            rad = math.radians(angle)
            
            # Main crystal line
            x1 = center_x
            y1 = center_y
            x2 = center_x + math.cos(rad) * length
            y2 = center_y + math.sin(rad) * length
            
            # Ice blue color with transparency
            alpha = max(30, 150 - length // 3)
            color = (150, 200, 255, alpha)
            
            draw.line([(x1, y1), (x2, y2)], fill=color, width=3)
            
            # Crystal branches
            if length % 40 == 0:
                branch_length = length * 0.3
                for branch_angle in [-30, 30]:
                    branch_rad = math.radians(angle + branch_angle)
                    bx = x2 + math.cos(branch_rad) * branch_length
                    by = y2 + math.sin(branch_rad) * branch_length
                    
                    draw.line([(x2, y2), (bx, by)], fill=color, width=2)
    
    # Add ice particles
    for _ in range(50):
        x = random.randint(0, size[0])
        y = random.randint(0, size[1])
        particle_size = random.randint(2, 8)
        
        color = (200, 230, 255, random.randint(80, 150))
        draw.ellipse([
            x - particle_size, y - particle_size,
            x + particle_size, y + particle_size
        ], fill=color)
    
    img = img.filter(ImageFilter.GaussianBlur(radius=2))
    return img

def create_shadow_tendrils(size=(1000, 1000)):
    """Create dark shadow tendril effect"""
    img = Image.new('RGBA', size, (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Create multiple shadow tendrils
    for tendril in range(12):
        # Random starting point
        start_x = random.randint(100, size[0] - 100)
        start_y = random.randint(100, size[1] - 100)
        
        # Create organic tendril path
        points = [(start_x, start_y)]
        current_x, current_y = start_x, start_y
        direction = random.uniform(0, 2 * math.pi)
        
        for segment in range(20):
            # Organic movement
            direction += random.uniform(-0.5, 0.5)
            length = random.randint(15, 40)
            
            current_x += math.cos(direction) * length
            current_y += math.sin(direction) * length
            
            # Keep within bounds
            current_x = max(50, min(size[0] - 50, current_x))
            current_y = max(50, min(size[1] - 50, current_y))
            
            points.append((current_x, current_y))
        
        # Draw tendril with varying thickness
        for i in range(len(points) - 1):
            thickness = max(1, 8 - i // 3)  # Thinner towards end
            alpha = max(20, 100 - i * 3)   # Fade towards end
            
            # Dark purple/black shadow color
            color = (50, 0, 80, alpha)
            
            draw.line([points[i], points[i + 1]], fill=color, width=thickness)
    
    img = img.filter(ImageFilter.GaussianBlur(radius=4))
    return img

def create_cosmic_nebula(size=(1000, 1000)):
    """Create cosmic nebula effect"""
    img = Image.new('RGBA', size, (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Create nebula clouds
    for cloud in range(5):
        center_x = random.randint(200, size[0] - 200)
        center_y = random.randint(200, size[1] - 200)
        
        # Nebula colors
        colors = [
            (255, 100, 200, 60),  # Pink
            (100, 150, 255, 60),  # Blue
            (200, 100, 255, 60),  # Purple
            (255, 200, 100, 60),  # Orange
        ]
        color = random.choice(colors)
        
        # Create cloud with multiple circles
        for layer in range(15):
            radius = random.randint(30, 120)
            offset_x = random.randint(-50, 50)
            offset_y = random.randint(-50, 50)
            
            x = center_x + offset_x
            y = center_y + offset_y
            
            # Vary alpha for cloud effect
            alpha = random.randint(20, color[3])
            cloud_color = (color[0], color[1], color[2], alpha)
            
            draw.ellipse([
                x - radius, y - radius,
                x + radius, y + radius
            ], fill=cloud_color)
    
    # Add stars
    for _ in range(100):
        x = random.randint(0, size[0])
        y = random.randint(0, size[1])
        star_size = random.randint(1, 4)
        
        # White/yellow stars
        brightness = random.randint(150, 255)
        color = (brightness, brightness, random.randint(200, 255), random.randint(100, 200))
        
        draw.ellipse([
            x - star_size, y - star_size,
            x + star_size, y + star_size
        ], fill=color)
    
    img = img.filter(ImageFilter.GaussianBlur(radius=6))
    return img

def main():
    """Generate advanced energy effect images"""
    energy_dir = "energy"
    
    advanced_effects = [
        # Unique advanced effects
        ("Holographic_Field#8", create_holographic_field),
        ("Quantum_Distortion#6", create_quantum_distortion),
        ("Digital_Matrix#7", create_digital_matrix),
        ("Fire_Aura#12", create_fire_aura),
        ("Ice_Crystal#10", create_ice_crystal),
        ("Shadow_Tendrils#9", create_shadow_tendrils),
        ("Cosmic_Nebula#4", create_cosmic_nebula),
    ]
    
    print("Creating advanced energy effects...")
    
    for effect_name, effect_func in advanced_effects:
        print(f"Creating {effect_name}...")
        img = effect_func()
        filename = f"{effect_name}.png"
        filepath = os.path.join(energy_dir, filename)
        img.save(filepath, 'PNG')
        print(f"Saved: {filepath}")
    
    print(f"\nCreated {len(advanced_effects)} advanced energy effects!")
    print("Advanced energy effects are ready!")

if __name__ == "__main__":
    main()
