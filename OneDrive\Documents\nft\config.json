{"collection": {"name": "Generated NFT Collection", "description": "A unique collection of 1000 generated NFTs", "total_supply": 25, "base_uri": "https://your-domain.com/metadata/"}, "traits": {"Background": {"trait_type": "Background", "required": true, "assets": {"Static Blue 2": {"file": "Static_Blue_2#50.jpg", "weight": 50}, "Static Blue 3": {"file": "Static_Blue_3#50.jpg", "weight": 50}, "Static Blue 4": {"file": "Static_Blue_4#50.jpg", "weight": 50}, "The Void 1": {"file": "The_Void_1#50.jpg", "weight": 50}, "The Void 2": {"file": "The_Void_2#50.jpg", "weight": 50}, "The Void 3": {"file": "The_Void_3#50.jpg", "weight": 50}, "The Void 4": {"file": "The_Void_4#25.jpg", "weight": 25}, "The Void 5": {"file": "The_Void_5#50.jpg", "weight": 50}, "The Void 6": {"file": "The_Void_6#50.jpg", "weight": 50}, "The Void 7": {"file": "The_Void_7#50.jpg", "weight": 50}, "The Void 8": {"file": "The_Void_8#25.jpg", "weight": 25}, "The Void 9": {"file": "The_Void_9#1.jpg", "weight": 1}, "The Void 10": {"file": "The_Void_10#25.jpg", "weight": 25}, "The Void 11": {"file": "The_Void_11#10.jpg", "weight": 10}}}, "Body": {"trait_type": "Body", "required": true, "assets": {"Standard Chassis 4": {"file": "Standard_Chassis_4#50.png", "weight": 50}, "Standard Chassis 5": {"file": "Standard_Chassis_5#50.png", "weight": 50}, "Cryo-Cooled": {"file": "Cryo-Cooled#25.png", "weight": 25}, "Overclocked Core": {"file": "Overclocked_Core#25.png", "weight": 25}, "Living Circuitry": {"file": "Living_Circuitry#1.png", "weight": 1}, "Sentient Nanites": {"file": "Sentient_Nanites#1.png", "weight": 1}}}, "Head": {"trait_type": "Head", "required": true, "assets": {"Standard Cranium 1": {"file": "Standard_Cranium_1#50.png", "weight": 50}, "Standard Cranium 2": {"file": "Standard_Cranium_2#50.png", "weight": 50}, "Standard Cranium 3": {"file": "Standard_Cranium_3#50.png", "weight": 50}, "Standard Cranium 5": {"file": "Standard_Cranium_5#50.png", "weight": 50}, "Standard Cranium 7": {"file": "Standard_Cranium_7#50.png", "weight": 50}, "Standard Cranium 8": {"file": "Standard_Cranium_8#50.png", "weight": 50}, "Standard Cranium 11": {"file": "Standard_Cranium_11#50.png", "weight": 50}, "Standard Cranium 12": {"file": "Standard_Cranium_12#50.png", "weight": 50}, "Model-S Skull": {"file": "Model-<PERSON>_Skull#50.png", "weight": 50}, "Observer Unit": {"file": "Observer_Unit#50.png", "weight": 50}, "Cracked Skull": {"file": "Cracked_Skull#25.png", "weight": 25}, "Data Port": {"file": "Data_Port#25.png", "weight": 25}, "Exposed Wiring": {"file": "Exposed_Wiring#25.png", "weight": 25}, "Enlightened Crown": {"file": "Enlightened_Crown#10.png", "weight": 10}, "Oracle Processor": {"file": "Oracle_Processor#1.png", "weight": 1}}}, "Energy": {"trait_type": "Energy", "required": true, "assets": {"None": {"file": "None#40.png", "weight": 40}, "Blue Aura": {"file": "Blue_Aura#30.png", "weight": 30}, "Electric Blue": {"file": "Electric_Blue#25.png", "weight": 25}, "Green Particles": {"file": "Green_Particles#25.png", "weight": 25}, "Blue Plasma": {"file": "Blue_Plasma#20.png", "weight": 20}, "Toxic Aura": {"file": "Toxic_Aura#20.png", "weight": 20}, "Red Lightning": {"file": "Red_Lightning#15.png", "weight": 15}, "Crimson Field": {"file": "Crimson_Field#15.png", "weight": 15}, "Purple Vortex": {"file": "Purple_Vortex#10.png", "weight": 10}, "Void Energy": {"file": "Void_Energy#5.png", "weight": 5}, "Rainbow Burst": {"file": "Rainbow_Burst#3.png", "weight": 3}, "Cosmic Storm": {"file": "Cosmic_Storm#1.png", "weight": 1}, "Fire Aura": {"file": "Fire_Aura#12.png", "weight": 12}, "Ice Crystal": {"file": "Ice_Crystal#10.png", "weight": 10}, "Shadow Tendrils": {"file": "Shadow_Tendrils#9.png", "weight": 9}, "Holographic Field": {"file": "Holographic_Field#8.png", "weight": 8}, "Digital Matrix": {"file": "Digital_Matrix#7.png", "weight": 7}, "Quantum Distortion": {"file": "Quantum_Distortion#6.png", "weight": 6}, "Cosmic Nebula": {"file": "Cosmic_Nebula#4.png", "weight": 4}, "Animated Lightning": {"file": "Animated_Lightning.gif", "weight": 8}, "Animated Fire": {"file": "Animated_Fire.gif", "weight": 6}, "Animated Plasma": {"file": "Animated_Plasma.gif", "weight": 5}, "Animated Hologram": {"file": "Animated_Hologram.gif", "weight": 3}, "Animated Particles": {"file": "Animated_Particles.gif", "weight": 2}, "Animated Smoke": {"file": "Animated_Smoke.gif", "weight": 7}, "Animated Toxic Smoke": {"file": "Animated_Toxic_Smoke.gif", "weight": 4}, "Animated Steam": {"file": "Animated_Steam.gif", "weight": 6}, "Animated Dark Smoke": {"file": "Animated_Dark_Smoke.gif", "weight": 3}, "Animated Magical Mist": {"file": "Animated_Magical_Mist.gif", "weight": 2}, "Animated Blinking Eyes": {"file": "Animated_Blinking_Eyes.gif", "weight": 8}, "Animated Laser Eyes": {"file": "Animated_Laser_Eyes.gif", "weight": 5}, "Animated Glowing Eyes": {"file": "Animated_Glowing_Eyes.gif", "weight": 6}, "Animated Cybernetic Eyes": {"file": "Animated_Cybernetic_Eyes.gif", "weight": 4}, "Animated Hypnotic Eyes": {"file": "Animated_Hypnotic_Eyes.gif", "weight": 3}, "Animated Solar System": {"file": "Animated_Solar_System.gif", "weight": 2}, "Animated Planet Rings": {"file": "Animated_Planet_Rings.gif", "weight": 4}, "Animated Gas Giant": {"file": "Animated_Gas_Giant.gif", "weight": 3}, "Animated Asteroid Belt": {"file": "Animated_Asteroid_Belt.gif", "weight": 3}, "Animated Nebula Planets": {"file": "Animated_Nebula_Planets.gif", "weight": 2}, "Realistic Cigarette Smoke": {"file": "Realistic_Cigarette_Smoke.gif", "weight": 6}, "Realistic Fire Smoke": {"file": "Realistic_Fire_Smoke.gif", "weight": 5}, "Realistic Industrial Smoke": {"file": "Realistic_Industrial_Smoke.gif", "weight": 4}, "Realistic Steam Locomotive": {"file": "Realistic_Steam_Locomotive.gif", "weight": 3}, "Angry Steam From Ears": {"file": "Angry_Steam_From_Ears.gif", "weight": 7}, "Rage Smoke Explosion": {"file": "Rage_Smoke_Explosion.gif", "weight": 4}, "Frustrated Puffing Smoke": {"file": "Frustrated_Puffing_Smoke.gif", "weight": 5}, "Volcanic Anger Smoke": {"file": "Volcanic_Anger_Smoke.gif", "weight": 3}}}}, "generation": {"ensure_uniqueness": true, "max_attempts": 10000, "output_format": "PNG", "image_size": [1000, 1000], "layer_order": ["Background", "Body", "Head", "Energy"]}}