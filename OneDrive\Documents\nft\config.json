{"collection": {"name": "Generated NFT Collection", "description": "A unique collection of 1000 generated NFTs", "total_supply": 1000, "base_uri": "https://your-domain.com/metadata/"}, "traits": {"Background": {"trait_type": "Background", "required": true, "assets": {"Static Blue 2": {"file": "Static_Blue_2#50.jpg", "weight": 50}, "Static Blue 3": {"file": "Static_Blue_3#50.jpg", "weight": 50}, "Static Blue 4": {"file": "Static_Blue_4#50.jpg", "weight": 50}, "The Void 1": {"file": "The_Void_1#50.jpg", "weight": 50}, "The Void 2": {"file": "The_Void_2#50.jpg", "weight": 50}, "The Void 3": {"file": "The_Void_3#50.jpg", "weight": 50}, "The Void 4": {"file": "The_Void_4#25.jpg", "weight": 25}, "The Void 5": {"file": "The_Void_5#50.jpg", "weight": 50}, "The Void 6": {"file": "The_Void_6#50.jpg", "weight": 50}, "The Void 7": {"file": "The_Void_7#50.jpg", "weight": 50}, "The Void 8": {"file": "The_Void_8#25.jpg", "weight": 25}, "The Void 9": {"file": "The_Void_9#1.jpg", "weight": 1}, "The Void 10": {"file": "The_Void_10#25.jpg", "weight": 25}, "The Void 11": {"file": "The_Void_11#10.jpg", "weight": 10}}}, "Body": {"trait_type": "Body", "required": true, "assets": {"Standard Chassis 4": {"file": "Standard_Chassis_4#50.png", "weight": 50}, "Standard Chassis 5": {"file": "Standard_Chassis_5#50.png", "weight": 50}, "Cryo-Cooled": {"file": "Cryo-Cooled#25.png", "weight": 25}, "Overclocked Core": {"file": "Overclocked_Core#25.png", "weight": 25}, "Living Circuitry": {"file": "Living_Circuitry#1.png", "weight": 1}, "Sentient Nanites": {"file": "Sentient_Nanites#1.png", "weight": 1}}}, "Head": {"trait_type": "Head", "required": true, "assets": {"Standard Cranium 1": {"file": "Standard_Cranium_1#50.png", "weight": 50}, "Standard Cranium 2": {"file": "Standard_Cranium_2#50.png", "weight": 50}, "Standard Cranium 3": {"file": "Standard_Cranium_3#50.png", "weight": 50}, "Standard Cranium 5": {"file": "Standard_Cranium_5#50.png", "weight": 50}, "Standard Cranium 7": {"file": "Standard_Cranium_7#50.png", "weight": 50}, "Standard Cranium 8": {"file": "Standard_Cranium_8#50.png", "weight": 50}, "Standard Cranium 11": {"file": "Standard_Cranium_11#50.png", "weight": 50}, "Standard Cranium 12": {"file": "Standard_Cranium_12#50.png", "weight": 50}, "Model-S Skull": {"file": "Model-<PERSON>_Skull#50.png", "weight": 50}, "Observer Unit": {"file": "Observer_Unit#50.png", "weight": 50}, "Cracked Skull": {"file": "Cracked_Skull#25.png", "weight": 25}, "Data Port": {"file": "Data_Port#25.png", "weight": 25}, "Exposed Wiring": {"file": "Exposed_Wiring#25.png", "weight": 25}, "Enlightened Crown": {"file": "Enlightened_Crown#10.png", "weight": 10}, "Oracle Processor": {"file": "Oracle_Processor#1.png", "weight": 1}}}}, "generation": {"ensure_uniqueness": true, "max_attempts": 10000, "output_format": "PNG", "image_size": [1000, 1000], "layer_order": ["Background", "Body", "Head"]}}