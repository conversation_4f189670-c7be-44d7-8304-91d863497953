#!/usr/bin/env python3
"""
CyberSouls Genesis NFT Generator
Generates unique NFT combinations with layered traits
"""

import json
import os
import random
import hashlib
import time
from PIL import Image
from typing import Dict, List, Tuple, Set
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NFTGenerator:
    def __init__(self, config_path: str = "config.json"):
        """Initialize the NFT generator"""
        self.config_path = config_path
        self.config = self.load_config()
        self.generated_combinations: Set[str] = set()
        self.nfts_data: List[Dict] = []
        
    def load_config(self) -> Dict:
        """Load configuration from JSON file"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Configuration file {self.config_path} not found")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in configuration file: {e}")
            raise
    
    def get_weighted_choice(self, trait_name: str) -> Tuple[str, Dict]:
        """Select a trait based on weighted probabilities"""
        trait_config = self.config['traits'][trait_name]
        assets = trait_config['assets']
        
        # Create weighted list
        choices = []
        weights = []
        
        for asset_name, asset_data in assets.items():
            choices.append((asset_name, asset_data))
            weights.append(asset_data['weight'])
        
        # Use random.choices for weighted selection
        selected = random.choices(choices, weights=weights, k=1)[0]
        return selected
    
    def generate_combination(self) -> Dict:
        """Generate a single NFT trait combination"""
        combination = {}
        
        for trait_name in self.config['traits'].keys():
            asset_name, asset_data = self.get_weighted_choice(trait_name)
            combination[trait_name] = {
                'name': asset_name,
                'file': asset_data['file'],
                'weight': asset_data['weight']
            }
        
        return combination
    
    def combination_to_hash(self, combination: Dict) -> str:
        """Convert combination to a unique hash string"""
        combo_str = ""
        for trait_name in sorted(combination.keys()):
            combo_str += f"{trait_name}:{combination[trait_name]['name']};"
        
        return hashlib.md5(combo_str.encode()).hexdigest()
    
    def is_unique_combination(self, combination: Dict) -> bool:
        """Check if the combination is unique"""
        combo_hash = self.combination_to_hash(combination)
        if combo_hash in self.generated_combinations:
            return False
        self.generated_combinations.add(combo_hash)
        return True
    
    def generate_unique_combinations(self, count: int) -> List[Dict]:
        """Generate specified number of unique combinations"""
        combinations = []
        attempts = 0
        max_attempts = self.config['generation'].get('max_attempts', 10000)
        
        logger.info(f"Generating {count} unique CyberSouls Genesis NFT combinations...")
        
        while len(combinations) < count and attempts < max_attempts:
            combination = self.generate_combination()
            
            if self.is_unique_combination(combination):
                combinations.append(combination)
                if len(combinations) % 50 == 0:
                    logger.info(f"Generated {len(combinations)}/{count} combinations")
            
            attempts += 1
        
        if len(combinations) < count:
            logger.warning(f"Could only generate {len(combinations)} unique combinations out of {count} requested")
        
        return combinations
    
    def create_nft_image(self, combination: Dict, token_id: int) -> str:
        """Create NFT image by layering traits"""
        img_config = self.config['generation']
        width, height = img_config['image_size']
        layer_order = img_config['layer_order']
        
        # Create base image
        final_image = Image.new('RGBA', (width, height), (0, 0, 0, 0))
        
        # Map trait names to folder names
        folder_mapping = {
            'Background': 'backgrounds',
            'Body': 'bodies',
            'Head': 'heads',
            'Energy': 'energy'
        }
        
        # Layer each trait
        for trait_name in layer_order:
            if trait_name in combination:
                trait_data = combination[trait_name]
                trait_folder = folder_mapping.get(trait_name, trait_name.lower() + 's')
                
                image_path = os.path.join(trait_folder, trait_data['file'])
                
                if os.path.exists(image_path):
                    try:
                        layer_image = Image.open(image_path).convert('RGBA')
                        
                        # Resize if necessary
                        if layer_image.size != (width, height):
                            layer_image = layer_image.resize((width, height), Image.Resampling.LANCZOS)
                        
                        # Composite the layer
                        final_image = Image.alpha_composite(final_image, layer_image)
                        
                    except Exception as e:
                        logger.error(f"Error loading {image_path}: {e}")
                else:
                    logger.warning(f"Image not found: {image_path}")
        
        # Save the final image
        output_dir = "output/images"
        os.makedirs(output_dir, exist_ok=True)
        
        output_path = os.path.join(output_dir, f"{token_id}.png")
        final_image.convert('RGB').save(output_path, 'PNG', quality=95)
        
        return output_path
    
    def create_metadata(self, combination: Dict, token_id: int, image_path: str) -> Dict:
        """Create metadata JSON for the NFT"""
        # Build attributes array
        attributes = []
        for trait_name, trait_data in combination.items():
            attributes.append({
                "trait_type": trait_name,
                "value": trait_data['name']
            })
        
        # Create metadata structure
        metadata = {
            "name": f"{self.config['collection']['name']} #{token_id}",
            "description": self.config['collection']['description'],
            "image": f"{self.config['collection']['base_uri']}{token_id}.png",
            "attributes": attributes,
            "properties": {
                "compiler": "CyberSouls Genesis Generator v1.0"
            }
        }
        
        return metadata
    
    def save_metadata(self, metadata: Dict, token_id: int) -> str:
        """Save metadata to JSON file"""
        output_dir = "output/metadata"
        os.makedirs(output_dir, exist_ok=True)
        
        output_path = os.path.join(output_dir, f"{token_id}.json")
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        return output_path
    
    def generate_collection(self):
        """Generate the complete NFT collection"""
        total_supply = self.config['collection']['total_supply']
        
        logger.info(f"🚀 Starting generation of {total_supply} CyberSouls Genesis NFTs...")
        
        # Generate unique combinations
        combinations = self.generate_unique_combinations(total_supply)
        
        # Create images and metadata
        for i, combination in enumerate(combinations, 1):
            logger.info(f"Creating CyberSoul #{i}/{len(combinations)}")
            
            # Create image
            image_path = self.create_nft_image(combination, i)
            
            # Create metadata
            metadata = self.create_metadata(combination, i, image_path)
            metadata_path = self.save_metadata(metadata, i)
            
            # Store NFT data
            self.nfts_data.append({
                'token_id': i,
                'combination': combination,
                'image_path': image_path,
                'metadata_path': metadata_path,
                'metadata': metadata
            })
        
        # Save collection summary
        self.save_collection_summary()
        
        logger.info(f"🎉 Successfully generated {len(combinations)} CyberSouls Genesis NFTs!")
        logger.info(f"📁 Images saved to: output/images/")
        logger.info(f"📄 Metadata saved to: output/metadata/")
    
    def save_collection_summary(self):
        """Save a summary of the generated collection"""
        summary = {
            'collection_info': self.config['collection'],
            'total_generated': len(self.nfts_data),
            'trait_distribution': self.calculate_trait_distribution(),
            'generation_stats': {
                'unique_combinations': len(self.generated_combinations),
                'total_possible_combinations': self.calculate_total_combinations()
            },
            'copyright_info': {
                'copyright': '© 2025 CyberSouls Genesis by CyberSouls Team',
                'license': 'NFT License - Ownership grants personal use rights',
                'created_by': 'CyberSouls Team',
                'generation_date': time.strftime('%Y-%m-%d %H:%M:%S'),
                'website': 'https://cybersouls.io',
                'social_media': {
                    'twitter': '@CyberSoulsNFT',
                    'discord': 'https://discord.gg/cybersouls',
                    'opensea': 'https://opensea.io/collection/cybersouls-genesis'
                }
            }
        }
        
        with open('output/collection_summary.json', 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
    
    def calculate_trait_distribution(self) -> Dict:
        """Calculate the distribution of traits in the generated collection"""
        distribution = {}
        
        for trait_name in self.config['traits'].keys():
            distribution[trait_name] = {}
            
            for nft_data in self.nfts_data:
                trait_value = nft_data['combination'][trait_name]['name']
                if trait_value not in distribution[trait_name]:
                    distribution[trait_name][trait_value] = 0
                distribution[trait_name][trait_value] += 1
        
        return distribution
    
    def calculate_total_combinations(self) -> int:
        """Calculate total possible unique combinations"""
        total = 1
        for trait_name, trait_config in self.config['traits'].items():
            total *= len(trait_config['assets'])
        return total

def main():
    """Main function to run the NFT generator"""
    try:
        generator = NFTGenerator()
        generator.generate_collection()
    except Exception as e:
        logger.error(f"Error during NFT generation: {e}")
        raise

if __name__ == "__main__":
    main()
