#!/usr/bin/env python3
"""
Create realistic animated smoke effects for NFT generation
"""

from PIL import Image, ImageDraw, ImageFilter, ImageEnhance
import os
import random
import math
import numpy as np

def create_realistic_cigarette_smoke(size=(1000, 1000), frames=60):
    """Create realistic cigarette smoke animation"""
    frame_list = []
    
    # Smoke source (cigarette tip)
    source_x, source_y = size[0] // 2, int(size[1] * 0.8)
    
    # Generate smoke particles with realistic physics
    smoke_particles = []
    for _ in range(120):
        smoke_particles.append({
            'birth_frame': random.randint(0, frames - 1),
            'life_span': random.randint(30, 50),
            'start_x': source_x + random.randint(-5, 5),
            'start_y': source_y + random.randint(-3, 3),
            'velocity_x': random.uniform(-0.3, 0.3),
            'velocity_y': random.uniform(-2.5, -1.5),
            'size': random.uniform(3, 8),
            'turbulence': random.uniform(0.1, 0.4),
            'density': random.uniform(0.6, 1.0)
        })
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Draw cigarette tip glow
        tip_glow = 0.7 + 0.3 * math.sin(frame * 0.2)
        tip_size = int(8 * tip_glow)
        tip_color = (255, int(100 + 50 * tip_glow), 0, int(200 * tip_glow))
        
        draw.ellipse([
            source_x - tip_size, source_y - tip_size//2,
            source_x + tip_size, source_y + tip_size//2
        ], fill=tip_color)
        
        # Draw smoke particles
        for particle in smoke_particles:
            age = frame - particle['birth_frame']
            
            if age < 0 or age >= particle['life_span']:
                continue
            
            # Realistic smoke physics
            age_factor = age / particle['life_span']
            
            # Wind and turbulence effects
            wind_x = math.sin(frame * 0.05) * 15 + math.cos(frame * 0.03) * 8
            turbulence_x = math.sin(age * particle['turbulence'] + frame * 0.1) * 20
            turbulence_y = math.cos(age * particle['turbulence'] * 0.7 + frame * 0.08) * 10
            
            # Particle position with realistic drift
            x = (particle['start_x'] + 
                 particle['velocity_x'] * age + 
                 wind_x * age_factor + 
                 turbulence_x)
            
            y = (particle['start_y'] + 
                 particle['velocity_y'] * age + 
                 turbulence_y - 
                 age_factor * 20)  # Additional upward drift
            
            # Smoke expansion and fading
            current_size = particle['size'] * (1 + age_factor * 3)
            current_opacity = int(particle['density'] * 120 * (1 - age_factor * 0.8))
            
            if current_opacity <= 0:
                continue
            
            # Realistic smoke color (gray with slight blue tint)
            gray_base = 140 + int(20 * (1 - age_factor))
            smoke_color = (gray_base, gray_base, gray_base + 10, current_opacity)
            
            # Draw particle with soft edges
            particle_radius = int(current_size)
            if particle_radius > 0:
                # Multiple layers for soft appearance
                for layer in range(3):
                    layer_size = particle_radius - layer * 2
                    layer_alpha = current_opacity // (layer + 1)
                    
                    if layer_size > 0 and layer_alpha > 0:
                        layer_color = (*smoke_color[:3], layer_alpha)
                        
                        x1, y1 = max(0, x - layer_size), max(0, y - layer_size)
                        x2, y2 = min(size[0], x + layer_size), min(size[1], y + layer_size)
                        
                        if x2 > x1 and y2 > y1:
                            draw.ellipse([x1, y1, x2, y2], fill=layer_color)
        
        # Apply realistic blur
        img = img.filter(ImageFilter.GaussianBlur(radius=4))
        frame_list.append(img)
    
    return frame_list

def create_realistic_fire_smoke(size=(1000, 1000), frames=45):
    """Create realistic fire smoke animation"""
    frame_list = []
    
    # Fire source (bottom center)
    fire_x, fire_y = size[0] // 2, int(size[1] * 0.9)
    
    # Generate fire smoke particles
    smoke_particles = []
    for _ in range(150):
        smoke_particles.append({
            'birth_frame': random.randint(0, frames - 1),
            'life_span': random.randint(25, 40),
            'start_x': fire_x + random.randint(-30, 30),
            'start_y': fire_y + random.randint(-10, 5),
            'velocity_x': random.uniform(-0.8, 0.8),
            'velocity_y': random.uniform(-4, -2),
            'size': random.uniform(5, 12),
            'heat_factor': random.uniform(0.3, 1.0),
            'density': random.uniform(0.7, 1.0)
        })
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Draw fire base
        fire_intensity = 0.8 + 0.2 * math.sin(frame * 0.4)
        fire_width = int(60 * fire_intensity)
        fire_height = int(40 * fire_intensity)
        
        # Fire layers
        fire_colors = [
            (255, 255, 100, int(150 * fire_intensity)),  # Yellow core
            (255, 150, 50, int(120 * fire_intensity)),   # Orange
            (255, 50, 50, int(100 * fire_intensity)),    # Red
        ]
        
        for i, fire_color in enumerate(fire_colors):
            layer_width = fire_width - i * 8
            layer_height = fire_height - i * 5
            
            if layer_width > 0 and layer_height > 0:
                draw.ellipse([
                    fire_x - layer_width, fire_y - layer_height,
                    fire_x + layer_width, fire_y + layer_height
                ], fill=fire_color)
        
        # Draw smoke particles
        for particle in smoke_particles:
            age = frame - particle['birth_frame']
            
            if age < 0 or age >= particle['life_span']:
                continue
            
            age_factor = age / particle['life_span']
            
            # Heat-driven convection
            heat_rise = particle['heat_factor'] * age * 0.5
            convection_x = math.sin(age * 0.2 + particle['heat_factor']) * 25
            
            # Particle position
            x = (particle['start_x'] + 
                 particle['velocity_x'] * age + 
                 convection_x)
            
            y = (particle['start_y'] + 
                 particle['velocity_y'] * age - 
                 heat_rise)
            
            # Smoke characteristics
            current_size = particle['size'] * (1 + age_factor * 2.5)
            
            # Fire smoke color transition (black to gray)
            if age_factor < 0.3:
                # Hot, dark smoke
                color_intensity = int(50 + age_factor * 100)
                smoke_color = (color_intensity, color_intensity, color_intensity)
            else:
                # Cooler, lighter smoke
                color_intensity = int(100 + (age_factor - 0.3) * 80)
                smoke_color = (color_intensity, color_intensity, color_intensity)
            
            current_opacity = int(particle['density'] * 140 * (1 - age_factor * 0.9))
            
            if current_opacity <= 0:
                continue
            
            # Draw particle
            particle_radius = int(current_size)
            if particle_radius > 0:
                full_color = (*smoke_color, current_opacity)
                
                x1, y1 = max(0, x - particle_radius), max(0, y - particle_radius)
                x2, y2 = min(size[0], x + particle_radius), min(size[1], y + particle_radius)
                
                if x2 > x1 and y2 > y1:
                    draw.ellipse([x1, y1, x2, y2], fill=full_color)
        
        img = img.filter(ImageFilter.GaussianBlur(radius=5))
        frame_list.append(img)
    
    return frame_list

def create_realistic_industrial_smoke(size=(1000, 1000), frames=80):
    """Create realistic industrial chimney smoke"""
    frame_list = []
    
    # Chimney position
    chimney_x, chimney_y = size[0] // 2, int(size[1] * 0.7)
    
    # Generate industrial smoke particles
    smoke_particles = []
    for _ in range(200):
        smoke_particles.append({
            'birth_frame': random.randint(0, frames - 1),
            'life_span': random.randint(40, 70),
            'start_x': chimney_x + random.randint(-8, 8),
            'start_y': chimney_y + random.randint(-5, 5),
            'velocity_x': random.uniform(-0.5, 0.5),
            'velocity_y': random.uniform(-3, -1.5),
            'size': random.uniform(4, 10),
            'density': random.uniform(0.8, 1.0),
            'pollution_level': random.uniform(0.5, 1.0)
        })
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Draw chimney outline
        chimney_width = 20
        chimney_height = 60
        chimney_color = (80, 80, 80, 200)
        
        draw.rectangle([
            chimney_x - chimney_width, chimney_y,
            chimney_x + chimney_width, chimney_y + chimney_height
        ], fill=chimney_color)
        
        # Strong wind effect
        wind_strength = 1.5 + math.sin(frame * 0.03) * 0.5
        wind_direction = math.cos(frame * 0.02) * 30
        
        # Draw smoke particles
        for particle in smoke_particles:
            age = frame - particle['birth_frame']
            
            if age < 0 or age >= particle['life_span']:
                continue
            
            age_factor = age / particle['life_span']
            
            # Industrial wind effects
            wind_x = wind_direction * age_factor * wind_strength
            wind_turbulence = math.sin(age * 0.1 + frame * 0.05) * 15
            
            # Particle position
            x = (particle['start_x'] + 
                 particle['velocity_x'] * age + 
                 wind_x + 
                 wind_turbulence)
            
            y = (particle['start_y'] + 
                 particle['velocity_y'] * age)
            
            # Industrial smoke characteristics
            current_size = particle['size'] * (1 + age_factor * 4)
            
            # Pollution-based coloring
            pollution_factor = particle['pollution_level']
            if pollution_factor > 0.8:
                # Heavy pollution - darker
                base_color = int(60 + age_factor * 40)
                smoke_color = (base_color, base_color, base_color)
            elif pollution_factor > 0.6:
                # Medium pollution - brownish
                base_color = int(80 + age_factor * 50)
                smoke_color = (base_color, base_color - 10, base_color - 20)
            else:
                # Light pollution - grayish
                base_color = int(100 + age_factor * 60)
                smoke_color = (base_color, base_color, base_color)
            
            current_opacity = int(particle['density'] * 130 * (1 - age_factor * 0.7))
            
            if current_opacity <= 0:
                continue
            
            # Draw particle
            particle_radius = int(current_size)
            if particle_radius > 0:
                full_color = (*smoke_color, current_opacity)
                
                x1, y1 = max(0, x - particle_radius), max(0, y - particle_radius)
                x2, y2 = min(size[0], x + particle_radius), min(size[1], y + particle_radius)
                
                if x2 > x1 and y2 > y1:
                    draw.ellipse([x1, y1, x2, y2], fill=full_color)
        
        img = img.filter(ImageFilter.GaussianBlur(radius=6))
        frame_list.append(img)
    
    return frame_list

def create_realistic_steam_locomotive(size=(1000, 1000), frames=50):
    """Create realistic steam locomotive smoke"""
    frame_list = []
    
    # Steam source
    steam_x, steam_y = size[0] // 2, int(size[1] * 0.75)
    
    # Generate steam particles
    steam_particles = []
    for _ in range(100):
        steam_particles.append({
            'birth_frame': random.randint(0, frames - 1),
            'life_span': random.randint(20, 35),
            'start_x': steam_x + random.randint(-12, 12),
            'start_y': steam_y + random.randint(-8, 8),
            'velocity_x': random.uniform(-1, 1),
            'velocity_y': random.uniform(-5, -3),
            'size': random.uniform(6, 14),
            'pressure': random.uniform(0.6, 1.0),
            'temperature': random.uniform(0.7, 1.0)
        })
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Locomotive rhythm (puffing effect)
        puff_cycle = (frame % 15) / 15.0
        puff_intensity = 1.0 if puff_cycle < 0.3 else 0.3
        
        # Draw steam source
        source_size = int(15 * puff_intensity)
        source_color = (255, 255, 255, int(180 * puff_intensity))
        
        draw.ellipse([
            steam_x - source_size, steam_y - source_size//2,
            steam_x + source_size, steam_y + source_size//2
        ], fill=source_color)
        
        # Draw steam particles
        for particle in steam_particles:
            age = frame - particle['birth_frame']
            
            if age < 0 or age >= particle['life_span']:
                continue
            
            age_factor = age / particle['life_span']
            
            # Steam expansion and cooling
            expansion_factor = particle['pressure'] * age_factor
            cooling_factor = 1 - (particle['temperature'] * age_factor * 0.8)
            
            # Movement with locomotive motion
            locomotive_sway = math.sin(frame * 0.3) * 10
            
            x = (particle['start_x'] + 
                 particle['velocity_x'] * age + 
                 locomotive_sway * age_factor)
            
            y = (particle['start_y'] + 
                 particle['velocity_y'] * age)
            
            # Steam characteristics
            current_size = particle['size'] * (1 + expansion_factor * 3)
            
            # Steam color (white to light gray)
            steam_intensity = int(255 * cooling_factor)
            steam_color = (steam_intensity, steam_intensity, steam_intensity)
            
            current_opacity = int(200 * cooling_factor * (1 - age_factor * 0.9))
            
            if current_opacity <= 0:
                continue
            
            # Draw particle
            particle_radius = int(current_size)
            if particle_radius > 0:
                full_color = (*steam_color, current_opacity)
                
                x1, y1 = max(0, x - particle_radius), max(0, y - particle_radius)
                x2, y2 = min(size[0], x + particle_radius), min(size[1], y + particle_radius)
                
                if x2 > x1 and y2 > y1:
                    draw.ellipse([x1, y1, x2, y2], fill=full_color)
        
        img = img.filter(ImageFilter.GaussianBlur(radius=3))
        frame_list.append(img)
    
    return frame_list

def save_animated_gif(frames, filename, duration=100):
    """Save frames as animated GIF"""
    if frames:
        frames[0].save(
            filename,
            save_all=True,
            append_images=frames[1:],
            duration=duration,
            loop=0,
            format='GIF'
        )

def main():
    """Generate realistic smoke effects"""
    energy_dir = "energy_animated"
    os.makedirs(energy_dir, exist_ok=True)
    
    print("Creating realistic smoke effects...")
    
    # Create realistic smoke effects
    smoke_effects = [
        ("Realistic_Cigarette_Smoke", create_realistic_cigarette_smoke, 120),
        ("Realistic_Fire_Smoke", create_realistic_fire_smoke, 140),
        ("Realistic_Industrial_Smoke", create_realistic_industrial_smoke, 100),
        ("Realistic_Steam_Locomotive", create_realistic_steam_locomotive, 160),
    ]
    
    for effect_name, effect_func, duration in smoke_effects:
        print(f"Creating {effect_name}...")
        frames = effect_func()
        
        # Save as GIF
        gif_path = os.path.join(energy_dir, f"{effect_name}.gif")
        save_animated_gif(frames, gif_path, duration)
        print(f"Saved: {gif_path}")
        
        # Also save first frame as PNG for static version
        png_path = os.path.join(energy_dir, f"{effect_name}.png")
        frames[0].save(png_path, 'PNG')
        print(f"Saved static: {png_path}")
    
    print(f"\nCreated {len(smoke_effects)} realistic smoke effects!")
    print("Realistic smoke effects are ready!")

if __name__ == "__main__":
    main()
