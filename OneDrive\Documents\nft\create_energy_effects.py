#!/usr/bin/env python3
"""
Create sample energy effect images for NFT generation
"""

from PIL import Image, ImageDraw, ImageFilter
import os
import random
import math

def create_energy_aura(size=(1000, 1000), color=(0, 255, 255, 100), intensity=50):
    """Create a glowing aura effect"""
    img = Image.new('RGBA', size, (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    center_x, center_y = size[0] // 2, size[1] // 2
    
    # Create multiple circles with decreasing opacity
    for i in range(intensity, 0, -5):
        alpha = max(10, color[3] - i)
        circle_color = (color[0], color[1], color[2], alpha)
        radius = i * 3
        
        draw.ellipse([
            center_x - radius, center_y - radius,
            center_x + radius, center_y + radius
        ], fill=circle_color)
    
    # Apply blur for smooth effect
    img = img.filter(ImageFilter.GaussianBlur(radius=8))
    return img

def create_lightning_effect(size=(1000, 1000), color=(255, 255, 0, 150)):
    """Create lightning bolt effects"""
    img = Image.new('RGBA', size, (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Create multiple lightning bolts
    for _ in range(random.randint(3, 7)):
        # Random starting point
        start_x = random.randint(100, size[0] - 100)
        start_y = random.randint(100, 200)
        
        # Create zigzag lightning path
        points = [(start_x, start_y)]
        current_x, current_y = start_x, start_y
        
        while current_y < size[1] - 100:
            # Random zigzag movement
            current_x += random.randint(-50, 50)
            current_y += random.randint(30, 80)
            current_x = max(50, min(size[0] - 50, current_x))
            points.append((current_x, current_y))
        
        # Draw lightning bolt
        for i in range(len(points) - 1):
            draw.line([points[i], points[i + 1]], fill=color, width=random.randint(3, 8))
    
    # Apply glow effect
    img = img.filter(ImageFilter.GaussianBlur(radius=2))
    return img

def create_plasma_field(size=(1000, 1000), color=(255, 0, 255, 80)):
    """Create plasma field effect"""
    img = Image.new('RGBA', size, (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Create plasma waves
    for wave in range(5):
        for angle in range(0, 360, 10):
            rad = math.radians(angle)
            center_x, center_y = size[0] // 2, size[1] // 2
            
            # Calculate wave position
            radius = 200 + wave * 50 + math.sin(rad * 3) * 30
            x = center_x + math.cos(rad) * radius
            y = center_y + math.sin(rad) * radius
            
            # Draw plasma dots
            dot_size = random.randint(5, 15)
            alpha = max(20, color[3] - wave * 15)
            dot_color = (color[0], color[1], color[2], alpha)
            
            draw.ellipse([
                x - dot_size, y - dot_size,
                x + dot_size, y + dot_size
            ], fill=dot_color)
    
    img = img.filter(ImageFilter.GaussianBlur(radius=5))
    return img

def create_energy_particles(size=(1000, 1000), color=(0, 255, 0, 120)):
    """Create floating energy particles"""
    img = Image.new('RGBA', size, (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Create random particles
    for _ in range(random.randint(20, 40)):
        x = random.randint(0, size[0])
        y = random.randint(0, size[1])
        particle_size = random.randint(3, 12)
        
        # Vary alpha for twinkling effect
        alpha = random.randint(50, color[3])
        particle_color = (color[0], color[1], color[2], alpha)
        
        draw.ellipse([
            x - particle_size, y - particle_size,
            x + particle_size, y + particle_size
        ], fill=particle_color)
    
    img = img.filter(ImageFilter.GaussianBlur(radius=1))
    return img

def main():
    """Generate all energy effect images"""
    energy_dir = "energy"
    os.makedirs(energy_dir, exist_ok=True)
    
    effects = [
        # No energy (transparent)
        ("None#40", lambda: Image.new('RGBA', (1000, 1000), (0, 0, 0, 0))),
        
        # Blue energy effects
        ("Blue_Aura#30", lambda: create_energy_aura(color=(0, 150, 255, 100))),
        ("Electric_Blue#25", lambda: create_lightning_effect(color=(0, 200, 255, 150))),
        ("Blue_Plasma#20", lambda: create_plasma_field(color=(100, 150, 255, 80))),
        
        # Green energy effects  
        ("Green_Particles#25", lambda: create_energy_particles(color=(0, 255, 100, 120))),
        ("Toxic_Aura#20", lambda: create_energy_aura(color=(150, 255, 0, 90))),
        
        # Red energy effects
        ("Red_Lightning#15", lambda: create_lightning_effect(color=(255, 50, 50, 140))),
        ("Crimson_Field#15", lambda: create_plasma_field(color=(255, 0, 100, 85))),
        
        # Purple/Rare effects
        ("Purple_Vortex#10", lambda: create_plasma_field(color=(200, 0, 255, 90))),
        ("Void_Energy#5", lambda: create_energy_aura(color=(100, 0, 200, 110))),
        
        # Ultra rare effects
        ("Rainbow_Burst#3", lambda: create_energy_particles(color=(255, 100, 200, 130))),
        ("Cosmic_Storm#1", lambda: create_lightning_effect(color=(255, 255, 255, 160))),
    ]
    
    print("Creating energy effect images...")
    
    for effect_name, effect_func in effects:
        print(f"Creating {effect_name}...")
        img = effect_func()
        filename = f"{effect_name}.png"
        filepath = os.path.join(energy_dir, filename)
        img.save(filepath, 'PNG')
        print(f"Saved: {filepath}")
    
    print(f"\nCreated {len(effects)} energy effects!")
    print("Energy effects are ready to use in your NFT generation.")

if __name__ == "__main__":
    main()
