#!/usr/bin/env python3
"""
Animated NFT Generator Script
Generates unique animated NFT combinations with moving energy effects
"""

import json
import os
import random
import hashlib
from PIL import Image
from typing import Dict, List, Tuple, Set
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AnimatedNFTGenerator:
    def __init__(self, config_path: str = "config.json"):
        """Initialize the animated NFT generator"""
        self.config_path = config_path
        self.config = self.load_config()
        self.generated_combinations: Set[str] = set()
        self.nfts_data: List[Dict] = []
        
    def load_config(self) -> Dict:
        """Load configuration from JSON file"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Configuration file {self.config_path} not found")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in configuration file: {e}")
            raise
    
    def get_weighted_choice(self, trait_name: str) -> Tuple[str, Dict]:
        """Select a trait based on weighted probabilities"""
        trait_config = self.config['traits'][trait_name]
        assets = trait_config['assets']
        
        # Create weighted list
        choices = []
        weights = []
        
        for asset_name, asset_data in assets.items():
            choices.append((asset_name, asset_data))
            weights.append(asset_data['weight'])
        
        # Use random.choices for weighted selection
        selected = random.choices(choices, weights=weights, k=1)[0]
        return selected
    
    def generate_combination(self) -> Dict:
        """Generate a single NFT trait combination"""
        combination = {}
        
        for trait_name in self.config['traits'].keys():
            asset_name, asset_data = self.get_weighted_choice(trait_name)
            combination[trait_name] = {
                'name': asset_name,
                'file': asset_data['file'],
                'weight': asset_data['weight']
            }
        
        return combination
    
    def combination_to_hash(self, combination: Dict) -> str:
        """Convert combination to a unique hash string"""
        combo_str = ""
        for trait_name in sorted(combination.keys()):
            combo_str += f"{trait_name}:{combination[trait_name]['name']};"
        
        return hashlib.md5(combo_str.encode()).hexdigest()
    
    def is_unique_combination(self, combination: Dict) -> bool:
        """Check if the combination is unique"""
        combo_hash = self.combination_to_hash(combination)
        if combo_hash in self.generated_combinations:
            return False
        self.generated_combinations.add(combo_hash)
        return True
    
    def generate_unique_combinations(self, count: int) -> List[Dict]:
        """Generate specified number of unique combinations"""
        combinations = []
        attempts = 0
        max_attempts = self.config['generation'].get('max_attempts', 10000)
        
        logger.info(f"Generating {count} unique animated NFT combinations...")
        
        while len(combinations) < count and attempts < max_attempts:
            combination = self.generate_combination()
            
            if self.is_unique_combination(combination):
                combinations.append(combination)
                if len(combinations) % 10 == 0:
                    logger.info(f"Generated {len(combinations)}/{count} combinations")
            
            attempts += 1
        
        if len(combinations) < count:
            logger.warning(f"Could only generate {len(combinations)} unique combinations out of {count} requested")
        
        return combinations
    
    def load_animated_frames(self, file_path: str) -> List[Image.Image]:
        """Load frames from animated GIF or return single frame for static images"""
        if not os.path.exists(file_path):
            logger.warning(f"File not found: {file_path}")
            return []
        
        try:
            img = Image.open(file_path)
            
            if hasattr(img, 'is_animated') and img.is_animated:
                # Load all frames from animated GIF
                frames = []
                for frame_num in range(img.n_frames):
                    img.seek(frame_num)
                    frame = img.copy().convert('RGBA')
                    frames.append(frame)
                return frames
            else:
                # Single frame for static images
                return [img.convert('RGBA')]
        except Exception as e:
            logger.error(f"Error loading {file_path}: {e}")
            return []
    
    def create_animated_nft(self, combination: Dict, token_id: int) -> str:
        """Create animated NFT by layering traits"""
        img_config = self.config['generation']
        width, height = img_config['image_size']
        layer_order = img_config['layer_order']
        
        # Load all frames for each layer
        layer_frames = {}
        max_frames = 1
        
        for trait_name in layer_order:
            if trait_name in combination:
                trait_data = combination[trait_name]
                folder_mapping = {
                    'Background': 'backgrounds',
                    'Body': 'bodies',
                    'Head': 'heads',
                    'Energy': 'energy_animated'  # Use animated energy folder
                }
                trait_folder = folder_mapping.get(trait_name, trait_name.lower() + 's')
                
                # For energy, try animated version first, then static
                if trait_name == 'Energy':
                    # Check if this is already an animated file
                    if trait_data['file'].endswith('.gif'):
                        animated_path = os.path.join(trait_folder, trait_data['file'])
                        if os.path.exists(animated_path):
                            frames = self.load_animated_frames(animated_path)
                            if frames:
                                layer_frames[trait_name] = frames
                                max_frames = max(max_frames, len(frames))
                                continue
                    else:
                        # Try to find animated version
                        animated_file = trait_data['file'].replace('.png', '.gif')
                        animated_path = os.path.join(trait_folder, f"Animated_{trait_data['name'].replace(' ', '_')}.gif")

                        if os.path.exists(animated_path):
                            frames = self.load_animated_frames(animated_path)
                            if frames:
                                layer_frames[trait_name] = frames
                                max_frames = max(max_frames, len(frames))
                                continue
                
                # Fallback to static image
                static_folder = folder_mapping.get(trait_name, trait_name.lower() + 's')
                if trait_name == 'Energy':
                    static_folder = 'energy'  # Use static energy folder as fallback
                
                image_path = os.path.join(static_folder, trait_data['file'])
                frames = self.load_animated_frames(image_path)
                if frames:
                    layer_frames[trait_name] = frames
        
        # Create animated frames
        final_frames = []
        
        for frame_num in range(max_frames):
            final_frame = Image.new('RGBA', (width, height), (0, 0, 0, 0))
            
            # Layer each trait
            for trait_name in layer_order:
                if trait_name in layer_frames:
                    frames = layer_frames[trait_name]
                    # Use modulo to loop shorter animations
                    frame_index = frame_num % len(frames)
                    layer_frame = frames[frame_index]
                    
                    # Resize if necessary
                    if layer_frame.size != (width, height):
                        layer_frame = layer_frame.resize((width, height), Image.Resampling.LANCZOS)
                    
                    # Composite the layer
                    final_frame = Image.alpha_composite(final_frame, layer_frame)
            
            final_frames.append(final_frame)
        
        # Save the animated NFT
        output_dir = "output/animated_images"
        os.makedirs(output_dir, exist_ok=True)
        
        if len(final_frames) > 1:
            # Save as animated GIF
            output_path = os.path.join(output_dir, f"{token_id}.gif")
            final_frames[0].save(
                output_path,
                save_all=True,
                append_images=final_frames[1:],
                duration=100,  # 100ms per frame
                loop=0,
                format='GIF'
            )
        else:
            # Save as static PNG
            output_path = os.path.join(output_dir, f"{token_id}.png")
            final_frames[0].convert('RGB').save(output_path, 'PNG', quality=95)
        
        return output_path
    
    def create_metadata(self, combination: Dict, token_id: int, image_path: str) -> Dict:
        """Create metadata JSON for the animated NFT"""
        # Build attributes array
        attributes = []
        for trait_name, trait_data in combination.items():
            attributes.append({
                "trait_type": trait_name,
                "value": trait_data['name']
            })
        
        # Determine if NFT is animated
        is_animated = image_path.endswith('.gif')
        
        # Create metadata structure
        metadata = {
            "name": f"{self.config['collection']['name']} #{token_id}",
            "description": self.config['collection']['description'],
            "image": f"{self.config['collection']['base_uri']}{token_id}.{'gif' if is_animated else 'png'}",
            "animation_url": f"{self.config['collection']['base_uri']}{token_id}.gif" if is_animated else None,
            "attributes": attributes,
            "properties": {
                "animated": is_animated,
                "compiler": "Animated NFT Generator v1.0"
            }
        }
        
        # Remove None values
        metadata = {k: v for k, v in metadata.items() if v is not None}
        
        return metadata
    
    def save_metadata(self, metadata: Dict, token_id: int) -> str:
        """Save metadata to JSON file"""
        output_dir = "output/metadata"
        os.makedirs(output_dir, exist_ok=True)
        
        output_path = os.path.join(output_dir, f"{token_id}.json")
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        return output_path
    
    def generate_collection(self):
        """Generate the complete animated NFT collection"""
        total_supply = self.config['collection']['total_supply']
        
        logger.info(f"Starting generation of {total_supply} animated NFTs...")
        
        # Generate unique combinations
        combinations = self.generate_unique_combinations(total_supply)
        
        # Create animated images and metadata
        for i, combination in enumerate(combinations, 1):
            logger.info(f"Creating animated NFT {i}/{len(combinations)}")
            
            # Create animated image
            image_path = self.create_animated_nft(combination, i)
            
            # Create metadata
            metadata = self.create_metadata(combination, i, image_path)
            metadata_path = self.save_metadata(metadata, i)
            
            # Store NFT data
            self.nfts_data.append({
                'token_id': i,
                'combination': combination,
                'image_path': image_path,
                'metadata_path': metadata_path,
                'metadata': metadata,
                'animated': image_path.endswith('.gif')
            })
        
        # Save collection summary
        self.save_collection_summary()
        
        animated_count = sum(1 for nft in self.nfts_data if nft['animated'])
        static_count = len(self.nfts_data) - animated_count
        
        logger.info(f"Successfully generated {len(combinations)} NFTs!")
        logger.info(f"Animated NFTs: {animated_count}")
        logger.info(f"Static NFTs: {static_count}")
        logger.info(f"Images saved to: output/animated_images/")
        logger.info(f"Metadata saved to: output/metadata/")
    
    def save_collection_summary(self):
        """Save a summary of the generated collection"""
        animated_count = sum(1 for nft in self.nfts_data if nft['animated'])
        
        summary = {
            'collection_info': self.config['collection'],
            'total_generated': len(self.nfts_data),
            'animated_nfts': animated_count,
            'static_nfts': len(self.nfts_data) - animated_count,
            'trait_distribution': self.calculate_trait_distribution(),
            'generation_stats': {
                'unique_combinations': len(self.generated_combinations),
                'total_possible_combinations': self.calculate_total_combinations()
            }
        }
        
        with open('output/animated_collection_summary.json', 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
    
    def calculate_trait_distribution(self) -> Dict:
        """Calculate the distribution of traits in the generated collection"""
        distribution = {}
        
        for trait_name in self.config['traits'].keys():
            distribution[trait_name] = {}
            
            for nft_data in self.nfts_data:
                trait_value = nft_data['combination'][trait_name]['name']
                if trait_value not in distribution[trait_name]:
                    distribution[trait_name][trait_value] = 0
                distribution[trait_name][trait_value] += 1
        
        return distribution
    
    def calculate_total_combinations(self) -> int:
        """Calculate total possible unique combinations"""
        total = 1
        for trait_name, trait_config in self.config['traits'].items():
            total *= len(trait_config['assets'])
        return total

def main():
    """Main function to run the animated NFT generator"""
    try:
        generator = AnimatedNFTGenerator()
        generator.generate_collection()
    except Exception as e:
        logger.error(f"Error during animated NFT generation: {e}")
        raise

if __name__ == "__main__":
    main()
