#!/usr/bin/env python3
"""
Upload CyberSouls Genesis NFT files to IPFS
IMPORTANT: Replace YOUR_API_KEY_HERE with your actual API key before running
"""

import requests
import os
import json
import time

# ⚠️ REPLACE THIS WITH YOUR ACTUAL API KEY ⚠️
API_KEY = "67cf20a6.114f474e9bdb4c22baa2d024dfb33660"
API_URL = "https://api.nft.storage/upload"

def check_api_key():
    """Check if API key is set"""
    if API_KEY == "YOUR_API_KEY_HERE":
        print("❌ ERROR: Please replace YOUR_API_KEY_HERE with your actual API key!")
        print("💡 Edit this file and replace line 12 with your NFT.Storage API key")
        return False
    return True

def upload_file_to_nft_storage(file_path, file_name):
    """Upload a single file to NFT.Storage"""
    headers = {"Authorization": f"Bearer {API_KEY}"}
    
    try:
        with open(file_path, "rb") as f:
            files = {"file": (file_name, f)}
            response = requests.post(API_URL, headers=headers, files=files)
            
            if response.status_code == 200:
                cid = response.json()["value"]["cid"]
                print(f"✅ {file_name} → ipfs://{cid}")
                return cid
            else:
                print(f"❌ Error: {response.text}")
                return None
                
    except Exception as e:
        print(f"❌ Exception: {e}")
        return None

def upload_folder_to_nft_storage(folder_path):
    """Upload all files in folder to IPFS"""
    print(f"\n🚀 Uploading: {folder_path}")
    
    if not os.path.exists(folder_path):
        print(f"❌ Folder not found: {folder_path}")
        return {}
    
    file_cids = {}
    files = sorted(os.listdir(folder_path))
    
    for i, file_name in enumerate(files, 1):
        file_path = os.path.join(folder_path, file_name)
        
        if os.path.isfile(file_path):
            print(f"📤 {i}/{len(files)}: {file_name}")
            cid = upload_file_to_nft_storage(file_path, file_name)
            
            if cid:
                file_cids[file_name] = cid
            
            # Rate limiting
            time.sleep(0.5)
    
    print(f"✅ Uploaded: {len(file_cids)}/{len(files)} files")
    return file_cids

def update_metadata_with_ipfs(metadata_folder, image_cids):
    """Update metadata files with IPFS image URLs"""
    print(f"\n🔄 Updating metadata with IPFS URLs...")
    
    for i in range(1, 1001):
        metadata_file = f"{i}.json"
        metadata_path = os.path.join(metadata_folder, metadata_file)
        
        if os.path.exists(metadata_path):
            try:
                with open(metadata_path, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                
                image_file = f"{i}.png"
                if image_file in image_cids:
                    metadata["image"] = f"ipfs://{image_cids[image_file]}"
                    
                    with open(metadata_path, 'w', encoding='utf-8') as f:
                        json.dump(metadata, f, indent=2, ensure_ascii=False)
                        
            except Exception as e:
                print(f"❌ Error updating NFT #{i}: {e}")
    
    print("✅ Metadata updated with IPFS URLs")

def update_config_base_uri(metadata_cids):
    """Update config.json with IPFS base_uri"""
    config_path = "config.json"

    if not os.path.exists(config_path):
        print(f"⚠️ Config file not found: {config_path}")
        return

    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)

        if metadata_cids:
            # Use first metadata CID as base
            first_metadata_cid = list(metadata_cids.values())[0]
            base_uri = f"ipfs://{first_metadata_cid}/"

            config["collection"]["base_uri"] = base_uri

            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            print(f"✅ Updated config.json base_uri: {base_uri}")
        else:
            print("⚠️ No metadata CIDs to update base_uri")

    except Exception as e:
        print(f"❌ Error updating config: {e}")

def save_results(image_cids, metadata_cids):
    """Save upload results"""
    results = {
        "collection": "CyberSouls Genesis",
        "upload_date": time.strftime("%Y-%m-%d %H:%M:%S"),
        "total_images": len(image_cids),
        "total_metadata": len(metadata_cids),
        "image_cids": image_cids,
        "metadata_cids": metadata_cids
    }

    with open("output/ipfs_results.json", "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    print("💾 Results saved to: output/ipfs_results.json")

def main():
    """Main upload function"""
    print("🌟 CyberSouls Genesis IPFS Upload")
    print("="*40)
    
    # Check API key
    if not check_api_key():
        return
    
    # Upload images first
    print("\n📸 STEP 1: Uploading Images")
    image_cids = upload_folder_to_nft_storage("output/images")
    
    if not image_cids:
        print("❌ No images uploaded. Stopping.")
        return
    
    # Update metadata
    print("\n📝 STEP 2: Updating Metadata")
    update_metadata_with_ipfs("output/metadata", image_cids)
    
    # Upload metadata
    print("\n📄 STEP 3: Uploading Metadata")
    metadata_cids = upload_folder_to_nft_storage("output/metadata")
    
    # Update config
    print("\n🔧 STEP 4: Updating Config")
    update_config_base_uri(metadata_cids)

    # Save results
    print("\n💾 STEP 5: Saving Results")
    save_results(image_cids, metadata_cids)
    
    # Summary
    print("\n" + "="*40)
    print("🎉 UPLOAD COMPLETE!")
    print(f"📊 Images: {len(image_cids)}")
    print(f"📊 Metadata: {len(metadata_cids)}")
    
    if image_cids:
        first_image = list(image_cids.keys())[0]
        print(f"\n🔗 Example: ipfs://{image_cids[first_image]}")
        print(f"🌐 Gateway: https://ipfs.io/ipfs/{image_cids[first_image]}")

if __name__ == "__main__":
    main()
