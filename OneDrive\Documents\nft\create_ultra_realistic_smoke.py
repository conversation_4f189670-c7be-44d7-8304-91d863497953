#!/usr/bin/env python3
"""
Create ultra realistic smoke effects based on HTML/JS animation
"""

from PIL import Image, ImageDraw, ImageFilter, ImageEnhance
import os
import random
import math

def create_ultra_realistic_smoke(size=(1000, 1000), frames=80):
    """Create ultra realistic smoke animation based on HTML/JS version"""
    frame_list = []
    
    # Smoke source (bottom center)
    source_x, source_y = size[0] // 2, int(size[1] * 0.9)
    
    # Particle system
    particles = []
    max_particles = 100
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Add new particles
        if len(particles) < max_particles:
            for _ in range(random.randint(2, 4)):
                particles.append({
                    'x': source_x + (random.random() - 0.5) * 100,
                    'y': source_y,
                    'radius': 20 + random.random() * 30,
                    'opacity': 0.2 + random.random() * 0.3,
                    'dx': (random.random() - 0.5) * 0.5,
                    'dy': -1 - random.random(),
                    'life': 0,
                    'max_life': 200 + random.random() * 100,
                    'turbulence_phase': random.random() * math.pi * 2
                })
        
        # Update and draw particles
        for i in range(len(particles) - 1, -1, -1):
            p = particles[i]
            
            # Update particle
            p['life'] += 1
            
            # Add turbulence
            turbulence_x = math.sin(p['life'] * 0.02 + p['turbulence_phase']) * 0.3
            turbulence_y = math.cos(p['life'] * 0.015 + p['turbulence_phase']) * 0.2
            
            p['x'] += p['dx'] + turbulence_x
            p['y'] += p['dy'] + turbulence_y
            
            # Expand particle over time
            age_factor = p['life'] / p['max_life']
            current_radius = p['radius'] * (1 + age_factor * 1.5)
            current_opacity = p['opacity'] * (1 - age_factor)
            
            # Remove dead particles
            if p['life'] > p['max_life'] or current_opacity <= 0:
                particles.pop(i)
                continue
            
            # Draw particle with radial gradient effect
            particle_alpha = int(current_opacity * 255)
            if particle_alpha > 0:
                # Create multiple layers for gradient effect
                for layer in range(5):
                    layer_radius = current_radius * (1 - layer * 0.15)
                    layer_alpha = particle_alpha // (layer + 1)
                    
                    if layer_radius > 0 and layer_alpha > 0:
                        # Smoke color - realistic gray
                        gray_value = 200 - layer * 30
                        smoke_color = (gray_value, gray_value, gray_value, layer_alpha)
                        
                        x1 = max(0, p['x'] - layer_radius)
                        y1 = max(0, p['y'] - layer_radius)
                        x2 = min(size[0], p['x'] + layer_radius)
                        y2 = min(size[1], p['y'] + layer_radius)
                        
                        if x2 > x1 and y2 > y1:
                            draw.ellipse([x1, y1, x2, y2], fill=smoke_color)
        
        # Apply realistic blur
        img = img.filter(ImageFilter.GaussianBlur(radius=8))
        frame_list.append(img)
    
    return frame_list

def create_cigarette_smoke_realistic(size=(1000, 1000), frames=100):
    """Create ultra realistic cigarette smoke"""
    frame_list = []
    
    # Cigarette tip position
    tip_x, tip_y = size[0] // 2, int(size[1] * 0.8)
    
    particles = []
    max_particles = 80
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Draw cigarette tip glow
        tip_glow = 0.7 + 0.3 * math.sin(frame * 0.1)
        tip_size = int(6 * tip_glow)
        tip_color = (255, int(100 + 50 * tip_glow), 0, int(200 * tip_glow))
        
        draw.ellipse([
            tip_x - tip_size, tip_y - tip_size//2,
            tip_x + tip_size, tip_y + tip_size//2
        ], fill=tip_color)
        
        # Add new smoke particles
        if len(particles) < max_particles:
            for _ in range(random.randint(1, 3)):
                particles.append({
                    'x': tip_x + random.randint(-3, 3),
                    'y': tip_y + random.randint(-2, 2),
                    'radius': 8 + random.random() * 15,
                    'opacity': 0.3 + random.random() * 0.4,
                    'dx': (random.random() - 0.5) * 0.3,
                    'dy': -0.8 - random.random() * 0.5,
                    'life': 0,
                    'max_life': 150 + random.random() * 80,
                    'wind_phase': random.random() * math.pi * 2,
                    'density': 0.6 + random.random() * 0.4
                })
        
        # Simulate wind effect
        wind_x = math.sin(frame * 0.02) * 0.5
        wind_y = math.cos(frame * 0.015) * 0.2
        
        # Update and draw particles
        for i in range(len(particles) - 1, -1, -1):
            p = particles[i]
            p['life'] += 1
            
            # Realistic smoke physics
            age_factor = p['life'] / p['max_life']
            
            # Wind and turbulence
            wind_effect_x = wind_x * age_factor * 2
            wind_effect_y = wind_y * age_factor
            turbulence_x = math.sin(p['life'] * 0.03 + p['wind_phase']) * 0.4
            turbulence_y = math.cos(p['life'] * 0.025 + p['wind_phase']) * 0.3
            
            p['x'] += p['dx'] + wind_effect_x + turbulence_x
            p['y'] += p['dy'] + wind_effect_y + turbulence_y
            
            # Smoke expansion and fading
            current_radius = p['radius'] * (1 + age_factor * 2.5)
            current_opacity = p['opacity'] * p['density'] * (1 - age_factor * 0.8)
            
            # Remove dead particles
            if p['life'] > p['max_life'] or current_opacity <= 0:
                particles.pop(i)
                continue
            
            # Draw realistic smoke particle
            particle_alpha = int(current_opacity * 255)
            if particle_alpha > 0:
                # Multiple layers for realistic appearance
                for layer in range(4):
                    layer_radius = current_radius * (1 - layer * 0.2)
                    layer_alpha = particle_alpha // (layer + 1)
                    
                    if layer_radius > 0 and layer_alpha > 0:
                        # Cigarette smoke color - slightly blue-gray
                        base_gray = 180 - layer * 20
                        smoke_color = (base_gray, base_gray, base_gray + 10, layer_alpha)
                        
                        x1 = max(0, p['x'] - layer_radius)
                        y1 = max(0, p['y'] - layer_radius)
                        x2 = min(size[0], p['x'] + layer_radius)
                        y2 = min(size[1], p['y'] + layer_radius)
                        
                        if x2 > x1 and y2 > y1:
                            draw.ellipse([x1, y1, x2, y2], fill=smoke_color)
        
        # Apply blur for realism
        img = img.filter(ImageFilter.GaussianBlur(radius=6))
        frame_list.append(img)
    
    return frame_list

def create_steam_kettle_realistic(size=(1000, 1000), frames=70):
    """Create realistic steam from kettle"""
    frame_list = []
    
    # Kettle spout position
    spout_x, spout_y = size[0] // 2, int(size[1] * 0.75)
    
    particles = []
    max_particles = 60
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Steam pressure effect
        pressure_cycle = (frame % 30) / 30.0
        pressure = 0.6 + 0.4 * math.sin(pressure_cycle * math.pi * 2)
        
        # Add new steam particles
        if len(particles) < max_particles:
            for _ in range(int(pressure * 4)):
                particles.append({
                    'x': spout_x + random.randint(-5, 5),
                    'y': spout_y + random.randint(-3, 3),
                    'radius': 10 + random.random() * 20,
                    'opacity': 0.4 + random.random() * 0.3,
                    'dx': (random.random() - 0.5) * 0.4,
                    'dy': -2 - random.random() * pressure,
                    'life': 0,
                    'max_life': 100 + random.random() * 60,
                    'heat_phase': random.random() * math.pi * 2,
                    'temperature': 0.8 + random.random() * 0.2
                })
        
        # Update and draw particles
        for i in range(len(particles) - 1, -1, -1):
            p = particles[i]
            p['life'] += 1
            
            age_factor = p['life'] / p['max_life']
            
            # Heat convection effects
            heat_rise = p['temperature'] * age_factor * 0.3
            heat_turbulence_x = math.sin(p['life'] * 0.04 + p['heat_phase']) * 0.6
            heat_turbulence_y = math.cos(p['life'] * 0.03 + p['heat_phase']) * 0.4
            
            p['x'] += p['dx'] + heat_turbulence_x
            p['y'] += p['dy'] - heat_rise + heat_turbulence_y
            
            # Steam characteristics
            current_radius = p['radius'] * (1 + age_factor * 2)
            cooling_factor = 1 - (age_factor * 0.7)
            current_opacity = p['opacity'] * cooling_factor
            
            # Remove dead particles
            if p['life'] > p['max_life'] or current_opacity <= 0:
                particles.pop(i)
                continue
            
            # Draw steam particle
            particle_alpha = int(current_opacity * 255)
            if particle_alpha > 0:
                # Steam layers
                for layer in range(3):
                    layer_radius = current_radius * (1 - layer * 0.25)
                    layer_alpha = particle_alpha // (layer + 1)
                    
                    if layer_radius > 0 and layer_alpha > 0:
                        # Steam color - white to light gray
                        steam_intensity = int(255 * cooling_factor)
                        steam_color = (steam_intensity, steam_intensity, steam_intensity, layer_alpha)
                        
                        x1 = max(0, p['x'] - layer_radius)
                        y1 = max(0, p['y'] - layer_radius)
                        x2 = min(size[0], p['x'] + layer_radius)
                        y2 = min(size[1], p['y'] + layer_radius)
                        
                        if x2 > x1 and y2 > y1:
                            draw.ellipse([x1, y1, x2, y2], fill=steam_color)
        
        img = img.filter(ImageFilter.GaussianBlur(radius=5))
        frame_list.append(img)
    
    return frame_list

def save_animated_gif(frames, filename, duration=100):
    """Save frames as animated GIF"""
    if frames:
        frames[0].save(
            filename,
            save_all=True,
            append_images=frames[1:],
            duration=duration,
            loop=0,
            format='GIF'
        )

def main():
    """Generate ultra realistic smoke effects"""
    energy_dir = "energy_animated"
    os.makedirs(energy_dir, exist_ok=True)
    
    print("Creating ultra realistic smoke effects...")
    
    # Create ultra realistic smoke effects
    ultra_effects = [
        ("Ultra_Realistic_Smoke", create_ultra_realistic_smoke, 100),
        ("Ultra_Cigarette_Smoke", create_cigarette_smoke_realistic, 120),
        ("Ultra_Steam_Kettle", create_steam_kettle_realistic, 140),
    ]
    
    for effect_name, effect_func, duration in ultra_effects:
        print(f"Creating {effect_name}...")
        frames = effect_func()
        
        # Save as GIF
        gif_path = os.path.join(energy_dir, f"{effect_name}.gif")
        save_animated_gif(frames, gif_path, duration)
        print(f"Saved: {gif_path}")
        
        # Also save first frame as PNG for static version
        png_path = os.path.join(energy_dir, f"{effect_name}.png")
        frames[0].save(png_path, 'PNG')
        print(f"Saved static: {png_path}")
    
    print(f"\nCreated {len(ultra_effects)} ultra realistic smoke effects!")
    print("Ultra realistic smoke effects are ready!")

if __name__ == "__main__":
    main()
