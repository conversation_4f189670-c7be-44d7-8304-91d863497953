#!/usr/bin/env python3
"""
Upload CyberSouls Genesis NFT files to IPFS via NFT.Storage
"""

import requests
import os
import json
import time
from typing import Dict, List

# NFT.Storage API configuration
API_KEY = os.getenv("NFT_STORAGE_API_KEY")
API_URL = "https://api.nft.storage/upload"

if not API_KEY:
    print("❌ Error: NFT_STORAGE_API_KEY environment variable not set!")
    print("💡 Set it with: set NFT_STORAGE_API_KEY=your_api_key_here")
    exit(1)

def upload_file_to_nft_storage(file_path: str, file_name: str) -> str:
    """Upload a single file to NFT.Storage and return IPFS CID"""
    headers = {"Authorization": f"Bearer {API_KEY}"}
    
    try:
        with open(file_path, "rb") as f:
            files = {"file": (file_name, f)}
            response = requests.post(API_URL, headers=headers, files=files)
            
            if response.status_code == 200:
                cid = response.json()["value"]["cid"]
                print(f"✅ {file_name} → ipfs://{cid}")
                return cid
            else:
                print(f"❌ Error uploading {file_name}: {response.text}")
                return None
                
    except Exception as e:
        print(f"❌ Exception uploading {file_name}: {e}")
        return None

def upload_folder_to_nft_storage(folder_path: str) -> Dict[str, str]:
    """Upload all files in a folder to NFT.Storage"""
    print(f"\n🚀 Uploading files from: {folder_path}")
    
    if not os.path.exists(folder_path):
        print(f"❌ Folder not found: {folder_path}")
        return {}
    
    file_cids = {}
    files = sorted(os.listdir(folder_path))
    
    for i, file_name in enumerate(files, 1):
        file_path = os.path.join(folder_path, file_name)
        
        if os.path.isfile(file_path):
            print(f"📤 Uploading {i}/{len(files)}: {file_name}")
            cid = upload_file_to_nft_storage(file_path, file_name)
            
            if cid:
                file_cids[file_name] = cid
            
            # Rate limiting - wait between uploads
            time.sleep(0.5)
    
    print(f"✅ Uploaded {len(file_cids)}/{len(files)} files successfully")
    return file_cids

def update_metadata_with_ipfs_urls(metadata_folder: str, image_cids: Dict[str, str]) -> Dict[str, str]:
    """Update metadata JSON files with IPFS image URLs"""
    print(f"\n🔄 Updating metadata with IPFS URLs...")
    
    updated_metadata_cids = {}
    
    for i in range(1, 1001):  # 1000 NFTs
        metadata_file = f"{i}.json"
        metadata_path = os.path.join(metadata_folder, metadata_file)
        
        if os.path.exists(metadata_path):
            try:
                # Read existing metadata
                with open(metadata_path, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                
                # Update image URL to IPFS
                image_file = f"{i}.png"
                if image_file in image_cids:
                    metadata["image"] = f"ipfs://{image_cids[image_file]}"
                    
                    # Save updated metadata
                    with open(metadata_path, 'w', encoding='utf-8') as f:
                        json.dump(metadata, f, indent=2, ensure_ascii=False)
                    
                    print(f"✅ Updated metadata for NFT #{i}")
                else:
                    print(f"⚠️ No IPFS CID found for image {image_file}")
                    
            except Exception as e:
                print(f"❌ Error updating metadata for NFT #{i}: {e}")
    
    print("✅ Metadata update complete")

def upload_updated_metadata(metadata_folder: str) -> Dict[str, str]:
    """Upload updated metadata files to IPFS"""
    print(f"\n🚀 Uploading updated metadata to IPFS...")
    return upload_folder_to_nft_storage(metadata_folder)

def save_ipfs_mapping(image_cids: Dict[str, str], metadata_cids: Dict[str, str]):
    """Save IPFS CID mapping to file"""
    mapping = {
        "collection": "CyberSouls Genesis",
        "total_nfts": len(image_cids),
        "upload_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "image_cids": image_cids,
        "metadata_cids": metadata_cids
    }
    
    with open("output/ipfs_mapping.json", "w", encoding="utf-8") as f:
        json.dump(mapping, f, indent=2, ensure_ascii=False)
    
    print(f"💾 IPFS mapping saved to: output/ipfs_mapping.json")

def main():
    """Main function to upload CyberSouls Genesis to IPFS"""
    print("🌟 CyberSouls Genesis IPFS Upload Starting...")
    
    # Define paths
    images_folder = "output/images"
    metadata_folder = "output/metadata"
    
    # Step 1: Upload images to IPFS
    print("\n" + "="*50)
    print("STEP 1: Uploading Images to IPFS")
    print("="*50)
    image_cids = upload_folder_to_nft_storage(images_folder)
    
    if not image_cids:
        print("❌ No images uploaded successfully. Stopping.")
        return
    
    # Step 2: Update metadata with IPFS image URLs
    print("\n" + "="*50)
    print("STEP 2: Updating Metadata with IPFS URLs")
    print("="*50)
    update_metadata_with_ipfs_urls(metadata_folder, image_cids)
    
    # Step 3: Upload updated metadata to IPFS
    print("\n" + "="*50)
    print("STEP 3: Uploading Metadata to IPFS")
    print("="*50)
    metadata_cids = upload_updated_metadata(metadata_folder)
    
    # Step 4: Save mapping
    print("\n" + "="*50)
    print("STEP 4: Saving IPFS Mapping")
    print("="*50)
    save_ipfs_mapping(image_cids, metadata_cids)
    
    # Summary
    print("\n" + "="*50)
    print("🎉 UPLOAD COMPLETE!")
    print("="*50)
    print(f"📊 Images uploaded: {len(image_cids)}")
    print(f"📊 Metadata uploaded: {len(metadata_cids)}")
    print(f"🌐 All files are now on IPFS!")
    print(f"📁 Check ipfs_mapping.json for all CIDs")
    
    # Show some examples
    if image_cids and metadata_cids:
        print(f"\n🔗 Example URLs:")
        first_image = list(image_cids.keys())[0]
        first_metadata = list(metadata_cids.keys())[0]
        print(f"   Image: ipfs://{image_cids[first_image]}")
        print(f"   Metadata: ipfs://{metadata_cids[first_metadata]}")

if __name__ == "__main__":
    main()
