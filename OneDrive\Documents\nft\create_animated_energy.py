#!/usr/bin/env python3
"""
Create animated energy effect images for NFT generation
"""

from PIL import Image, ImageDraw, ImageFilter
import os
import random
import math

def create_animated_lightning(size=(1000, 1000), frames=8):
    """Create animated lightning effect"""
    frame_list = []
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Create multiple lightning bolts with animation
        for bolt in range(random.randint(2, 5)):
            # Random starting point
            start_x = random.randint(100, size[0] - 100)
            start_y = random.randint(50, 150)
            
            # Create animated zigzag lightning path
            points = [(start_x, start_y)]
            current_x, current_y = start_x, start_y
            
            # Animation offset
            anim_offset = frame * 20
            
            while current_y < size[1] - 100:
                # Animated zigzag movement
                current_x += random.randint(-40, 40) + math.sin(frame * 0.5) * 10
                current_y += random.randint(40, 90)
                current_x = max(50, min(size[0] - 50, current_x))
                points.append((current_x, current_y))
            
            # Draw lightning bolt with animated intensity
            intensity = 100 + int(50 * math.sin(frame * 0.8))
            color = (255, 255, 0, intensity)
            
            for i in range(len(points) - 1):
                width = random.randint(2, 6) + int(2 * math.sin(frame * 0.3))
                draw.line([points[i], points[i + 1]], fill=color, width=width)
        
        # Apply glow effect
        img = img.filter(ImageFilter.GaussianBlur(radius=2))
        frame_list.append(img)
    
    return frame_list

def create_animated_fire(size=(1000, 1000), frames=12):
    """Create animated fire aura effect"""
    frame_list = []
    
    center_x, center_y = size[0] // 2, size[1] // 2
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Create animated flame particles
        for particle in range(150):
            # Animated position
            angle = random.uniform(0, 2 * math.pi) + frame * 0.1
            base_distance = random.uniform(50, 250)
            
            # Flame flicker animation
            flicker = math.sin(frame * 0.5 + particle * 0.1) * 30
            distance = base_distance + flicker
            
            x = center_x + math.cos(angle) * distance
            y = center_y + math.sin(angle) * distance
            
            # Animated flame colors
            heat = random.uniform(0, 1) + math.sin(frame * 0.3) * 0.2
            heat = max(0, min(1, heat))
            
            if heat < 0.3:
                color = (255, int(80 + heat * 175), 0, random.randint(40, 100))
            elif heat < 0.7:
                color = (255, int(120 + heat * 135), int(heat * 80), random.randint(60, 120))
            else:
                color = (255, 255, int(180 + heat * 75), random.randint(80, 140))
            
            # Animated particle size
            base_size = random.randint(3, 15)
            size_variation = int(5 * math.sin(frame * 0.4 + particle * 0.05))
            particle_size = base_size + size_variation
            
            # Ensure valid coordinates
            x1, y1 = max(0, x - particle_size), max(0, y - particle_size)
            x2, y2 = min(size[0], x + particle_size), min(size[1], y + particle_size)

            if x2 > x1 and y2 > y1:
                draw.ellipse([x1, y1, x2, y2], fill=color)
        
        img = img.filter(ImageFilter.GaussianBlur(radius=6))
        frame_list.append(img)
    
    return frame_list

def create_animated_plasma(size=(1000, 1000), frames=16):
    """Create animated plasma field effect"""
    frame_list = []
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        center_x, center_y = size[0] // 2, size[1] // 2
        
        # Create animated plasma waves
        for wave in range(6):
            for angle in range(0, 360, 8):
                rad = math.radians(angle)
                
                # Animated wave calculation
                time_offset = frame * 0.2
                radius = 150 + wave * 40 + math.sin(rad * 3 + time_offset) * 25
                
                # Plasma movement
                x = center_x + math.cos(rad + time_offset) * radius
                y = center_y + math.sin(rad + time_offset) * radius
                
                # Animated color cycling
                color_cycle = (frame + angle + wave * 60) % 360
                if color_cycle < 120:
                    color = (255, 0, 255, 60)  # Magenta
                elif color_cycle < 240:
                    color = (0, 255, 255, 60)  # Cyan
                else:
                    color = (255, 255, 0, 60)  # Yellow
                
                # Animated dot size
                dot_size = 8 + int(4 * math.sin(frame * 0.3 + angle * 0.01))
                
                # Ensure valid coordinates
                x1, y1 = max(0, x - dot_size), max(0, y - dot_size)
                x2, y2 = min(size[0], x + dot_size), min(size[1], y + dot_size)

                if x2 > x1 and y2 > y1:
                    draw.ellipse([x1, y1, x2, y2], fill=color)
        
        img = img.filter(ImageFilter.GaussianBlur(radius=4))
        frame_list.append(img)
    
    return frame_list

def create_animated_hologram(size=(1000, 1000), frames=10):
    """Create animated holographic effect"""
    frame_list = []
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Animated holographic scan lines
        for i in range(0, size[1], 6):
            # Animated wave effect
            wave_offset = frame * 5
            wave_y = i + math.sin((i + wave_offset) * 0.02) * 10
            
            # Color cycling
            color_phase = (frame + i) % 60
            if color_phase < 20:
                color = (0, 255, 255, 40)  # Cyan
            elif color_phase < 40:
                color = (255, 0, 255, 40)  # Magenta
            else:
                color = (255, 255, 0, 40)  # Yellow
            
            # Draw animated scan line
            for x in range(0, size[0], 8):
                line_x = x + math.sin(wave_offset * 0.01) * 5
                draw.line([(line_x, wave_y), (line_x + 4, wave_y)], fill=color, width=2)
        
        # Add animated glitch effect
        if frame % 3 == 0:  # Glitch every 3rd frame
            for _ in range(5):
                glitch_x = random.randint(0, size[0])
                glitch_y = random.randint(0, size[1])
                glitch_w = random.randint(50, 200)
                glitch_h = random.randint(2, 8)
                
                draw.rectangle([
                    glitch_x, glitch_y,
                    glitch_x + glitch_w, glitch_y + glitch_h
                ], fill=(255, 255, 255, 80))
        
        img = img.filter(ImageFilter.GaussianBlur(radius=1))
        frame_list.append(img)
    
    return frame_list

def create_animated_energy_particles(size=(1000, 1000), frames=20):
    """Create animated floating energy particles"""
    frame_list = []
    
    # Generate particle positions that persist across frames
    particles = []
    for _ in range(40):
        particles.append({
            'start_x': random.randint(0, size[0]),
            'start_y': random.randint(0, size[1]),
            'speed_x': random.uniform(-2, 2),
            'speed_y': random.uniform(-3, -1),
            'size': random.randint(4, 12),
            'color_phase': random.randint(0, 360)
        })
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        for particle in particles:
            # Animated particle movement
            x = (particle['start_x'] + particle['speed_x'] * frame) % size[0]
            y = (particle['start_y'] + particle['speed_y'] * frame) % size[1]
            
            # Animated twinkling
            twinkle = math.sin(frame * 0.3 + particle['color_phase'] * 0.01)
            alpha = int(60 + 40 * twinkle)
            
            # Animated color shifting
            color_shift = (particle['color_phase'] + frame * 10) % 360
            if color_shift < 120:
                color = (0, 255, 100, alpha)  # Green
            elif color_shift < 240:
                color = (100, 100, 255, alpha)  # Blue
            else:
                color = (255, 100, 200, alpha)  # Pink
            
            # Animated size pulsing
            size_pulse = int(particle['size'] + 3 * math.sin(frame * 0.2 + particle['color_phase'] * 0.02))
            
            # Ensure valid coordinates
            x1, y1 = max(0, x - size_pulse), max(0, y - size_pulse)
            x2, y2 = min(size[0], x + size_pulse), min(size[1], y + size_pulse)

            if x2 > x1 and y2 > y1:
                draw.ellipse([x1, y1, x2, y2], fill=color)
        
        img = img.filter(ImageFilter.GaussianBlur(radius=1))
        frame_list.append(img)
    
    return frame_list

def save_animated_gif(frames, filename, duration=100):
    """Save frames as animated GIF"""
    if frames:
        frames[0].save(
            filename,
            save_all=True,
            append_images=frames[1:],
            duration=duration,
            loop=0,
            format='GIF'
        )

def main():
    """Generate animated energy effects"""
    energy_dir = "energy_animated"
    os.makedirs(energy_dir, exist_ok=True)
    
    print("Creating animated energy effects...")
    
    # Create animated effects
    effects = [
        ("Animated_Lightning", create_animated_lightning, 150),
        ("Animated_Fire", create_animated_fire, 120),
        ("Animated_Plasma", create_animated_plasma, 100),
        ("Animated_Hologram", create_animated_hologram, 200),
        ("Animated_Particles", create_animated_energy_particles, 80),
    ]
    
    for effect_name, effect_func, duration in effects:
        print(f"Creating {effect_name}...")
        frames = effect_func()
        
        # Save as GIF
        gif_path = os.path.join(energy_dir, f"{effect_name}.gif")
        save_animated_gif(frames, gif_path, duration)
        print(f"Saved: {gif_path}")
        
        # Also save first frame as PNG for static version
        png_path = os.path.join(energy_dir, f"{effect_name}.png")
        frames[0].save(png_path, 'PNG')
        print(f"Saved static: {png_path}")
    
    print(f"\nCreated {len(effects)} animated energy effects!")
    print("Animated effects are ready!")

if __name__ == "__main__":
    main()
