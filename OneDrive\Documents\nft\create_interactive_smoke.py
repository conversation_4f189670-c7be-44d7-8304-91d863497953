#!/usr/bin/env python3
"""
Create interactive smoke effects based on HTML/JS mouse-following animation
"""

from PIL import Image, ImageDraw, ImageFilter, ImageEnhance
import os
import random
import math

def create_mouse_following_smoke(size=(1000, 1000), frames=120):
    """Create smoke that follows a simulated mouse path"""
    frame_list = []
    
    # Simulate mouse movement path
    mouse_path = []
    center_x, center_y = size[0] // 2, size[1] // 2
    
    # Generate realistic mouse movement
    for frame in range(frames):
        # Circular movement with random variations
        angle = frame * 0.05
        radius = 100 + math.sin(frame * 0.02) * 50
        
        mouse_x = center_x + math.cos(angle) * radius + random.randint(-20, 20)
        mouse_y = center_y + math.sin(angle) * radius + random.randint(-20, 20)
        
        # Keep within bounds
        mouse_x = max(50, min(size[0] - 50, mouse_x))
        mouse_y = max(50, min(size[1] - 50, mouse_y))
        
        mouse_path.append((mouse_x, mouse_y))
    
    # Particle system
    particles = []
    max_particles = 150
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Current mouse position
        mouse_x, mouse_y = mouse_path[frame]
        
        # Add new particles at mouse position
        if len(particles) < max_particles:
            for _ in range(2):  # 2 particles per frame
                particles.append({
                    'x': mouse_x + (random.random() - 0.5) * 20,
                    'y': mouse_y + (random.random() - 0.5) * 20,
                    'size': random.random() * 40 + 30,
                    'speed_x': random.random() * 2 - 1,
                    'speed_y': (random.random() * 0.5 + 0.2) * -1,
                    'opacity': 1.0,
                    'angle': random.random() * 360,
                    'spin': 1 if random.random() < 0.5 else -1,
                    'life': 0,
                    'max_life': 100 + random.random() * 50
                })
        
        # Update and draw particles
        for i in range(len(particles) - 1, -1, -1):
            p = particles[i]
            p['life'] += 1
            
            # Update particle position
            p['x'] += p['speed_x']
            p['y'] += p['speed_y']
            p['angle'] += p['spin'] * 0.1
            
            # Fade out over time
            age_factor = p['life'] / p['max_life']
            p['opacity'] = max(0, 1 - age_factor)
            p['size'] = max(0.2, p['size'] - 0.1)
            
            # Remove dead particles
            if p['size'] <= 0.2 or p['opacity'] <= 0.01 or p['life'] > p['max_life']:
                particles.pop(i)
                continue
            
            # Draw particle with rotation effect
            if p['opacity'] > 0:
                # Create multiple layers for realistic smoke
                for layer in range(4):
                    layer_size = p['size'] * (1 - layer * 0.15)
                    layer_alpha = int(p['opacity'] * 255 / (layer + 1))
                    
                    if layer_size > 0 and layer_alpha > 0:
                        # Smoke color with slight variations
                        gray_base = 200 - layer * 25
                        smoke_color = (gray_base, gray_base, gray_base + 5, layer_alpha)
                        
                        # Apply rotation (simplified)
                        rotation_offset = math.sin(math.radians(p['angle'])) * 5
                        
                        x1 = max(0, p['x'] - layer_size + rotation_offset)
                        y1 = max(0, p['y'] - layer_size)
                        x2 = min(size[0], p['x'] + layer_size + rotation_offset)
                        y2 = min(size[1], p['y'] + layer_size)
                        
                        if x2 > x1 and y2 > y1:
                            draw.ellipse([x1, y1, x2, y2], fill=smoke_color)
        
        # Apply blur for realism
        img = img.filter(ImageFilter.GaussianBlur(radius=6))
        frame_list.append(img)
    
    return frame_list

def create_swirling_smoke_trail(size=(1000, 1000), frames=100):
    """Create swirling smoke trail effect"""
    frame_list = []
    
    # Create swirling path
    center_x, center_y = size[0] // 2, size[1] // 2
    particles = []
    max_particles = 120
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Swirling motion source
        swirl_angle = frame * 0.1
        swirl_radius = 80 + math.sin(frame * 0.03) * 30
        
        source_x = center_x + math.cos(swirl_angle) * swirl_radius
        source_y = center_y + math.sin(swirl_angle) * swirl_radius
        
        # Add new particles
        if len(particles) < max_particles:
            for _ in range(3):
                particles.append({
                    'x': source_x + random.randint(-10, 10),
                    'y': source_y + random.randint(-10, 10),
                    'size': random.random() * 35 + 25,
                    'speed_x': math.cos(swirl_angle + math.pi/2) * 0.5 + random.uniform(-0.3, 0.3),
                    'speed_y': math.sin(swirl_angle + math.pi/2) * 0.5 + random.uniform(-0.8, -0.2),
                    'opacity': 0.8 + random.random() * 0.2,
                    'angle': random.random() * 360,
                    'spin': random.uniform(-2, 2),
                    'life': 0,
                    'max_life': 80 + random.random() * 40,
                    'swirl_factor': random.uniform(0.02, 0.05)
                })
        
        # Update and draw particles
        for i in range(len(particles) - 1, -1, -1):
            p = particles[i]
            p['life'] += 1
            
            # Swirling motion
            swirl_force_x = math.cos(p['life'] * p['swirl_factor']) * 0.3
            swirl_force_y = math.sin(p['life'] * p['swirl_factor']) * 0.3
            
            p['x'] += p['speed_x'] + swirl_force_x
            p['y'] += p['speed_y'] + swirl_force_y
            p['angle'] += p['spin']
            
            # Aging effects
            age_factor = p['life'] / p['max_life']
            p['opacity'] = max(0, p['opacity'] * (1 - age_factor * 0.8))
            current_size = p['size'] * (1 + age_factor * 0.5)
            
            # Remove dead particles
            if p['life'] > p['max_life'] or p['opacity'] <= 0.01:
                particles.pop(i)
                continue
            
            # Draw swirling particle
            if p['opacity'] > 0:
                particle_alpha = int(p['opacity'] * 255)
                
                # Multiple layers for depth
                for layer in range(3):
                    layer_size = current_size * (1 - layer * 0.2)
                    layer_alpha = particle_alpha // (layer + 1)
                    
                    if layer_size > 0 and layer_alpha > 0:
                        # Swirling smoke color
                        base_color = 180 - layer * 20
                        smoke_color = (base_color, base_color, base_color + 10, layer_alpha)
                        
                        # Apply swirl rotation
                        angle_rad = math.radians(p['angle'])
                        offset_x = math.cos(angle_rad) * layer * 2
                        offset_y = math.sin(angle_rad) * layer * 2
                        
                        x1 = max(0, p['x'] - layer_size + offset_x)
                        y1 = max(0, p['y'] - layer_size + offset_y)
                        x2 = min(size[0], p['x'] + layer_size + offset_x)
                        y2 = min(size[1], p['y'] + layer_size + offset_y)
                        
                        if x2 > x1 and y2 > y1:
                            draw.ellipse([x1, y1, x2, y2], fill=smoke_color)
        
        img = img.filter(ImageFilter.GaussianBlur(radius=5))
        frame_list.append(img)
    
    return frame_list

def create_dynamic_smoke_burst(size=(1000, 1000), frames=80):
    """Create dynamic smoke burst effect"""
    frame_list = []
    
    center_x, center_y = size[0] // 2, size[1] // 2
    particles = []
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Burst intensity varies over time
        burst_intensity = math.sin(frame * 0.1) * 0.5 + 0.5
        
        # Add burst particles
        if frame % 5 == 0:  # Every 5 frames
            burst_count = int(burst_intensity * 8 + 2)
            for _ in range(burst_count):
                angle = random.uniform(0, 2 * math.pi)
                speed = random.uniform(1, 4) * burst_intensity
                
                particles.append({
                    'x': center_x + random.randint(-15, 15),
                    'y': center_y + random.randint(-15, 15),
                    'size': random.uniform(20, 50),
                    'speed_x': math.cos(angle) * speed,
                    'speed_y': math.sin(angle) * speed - 0.5,  # Slight upward bias
                    'opacity': 0.7 + random.random() * 0.3,
                    'angle': random.uniform(0, 360),
                    'spin': random.uniform(-3, 3),
                    'life': 0,
                    'max_life': 60 + random.random() * 30,
                    'burst_factor': burst_intensity
                })
        
        # Update and draw particles
        for i in range(len(particles) - 1, -1, -1):
            p = particles[i]
            p['life'] += 1
            
            # Dynamic movement
            p['x'] += p['speed_x']
            p['y'] += p['speed_y']
            p['angle'] += p['spin']
            
            # Slow down over time
            p['speed_x'] *= 0.98
            p['speed_y'] *= 0.98
            
            # Aging
            age_factor = p['life'] / p['max_life']
            p['opacity'] = max(0, p['opacity'] * (1 - age_factor))
            current_size = p['size'] * (1 + age_factor * p['burst_factor'])
            
            # Remove dead particles
            if p['life'] > p['max_life'] or p['opacity'] <= 0.01:
                particles.pop(i)
                continue
            
            # Draw dynamic particle
            if p['opacity'] > 0:
                particle_alpha = int(p['opacity'] * 255)
                
                # Dynamic layers
                layer_count = 3 + int(p['burst_factor'] * 2)
                for layer in range(layer_count):
                    layer_size = current_size * (1 - layer * 0.15)
                    layer_alpha = particle_alpha // (layer + 1)
                    
                    if layer_size > 0 and layer_alpha > 0:
                        # Dynamic color based on burst factor
                        color_intensity = int(160 + p['burst_factor'] * 40 - layer * 15)
                        smoke_color = (color_intensity, color_intensity, color_intensity + 5, layer_alpha)
                        
                        # Dynamic rotation
                        angle_rad = math.radians(p['angle'])
                        rotation_x = math.cos(angle_rad) * layer * 3
                        rotation_y = math.sin(angle_rad) * layer * 3
                        
                        x1 = max(0, p['x'] - layer_size + rotation_x)
                        y1 = max(0, p['y'] - layer_size + rotation_y)
                        x2 = min(size[0], p['x'] + layer_size + rotation_x)
                        y2 = min(size[1], p['y'] + layer_size + rotation_y)
                        
                        if x2 > x1 and y2 > y1:
                            draw.ellipse([x1, y1, x2, y2], fill=smoke_color)
        
        img = img.filter(ImageFilter.GaussianBlur(radius=7))
        frame_list.append(img)
    
    return frame_list

def save_animated_gif(frames, filename, duration=100):
    """Save frames as animated GIF"""
    if frames:
        frames[0].save(
            filename,
            save_all=True,
            append_images=frames[1:],
            duration=duration,
            loop=0,
            format='GIF'
        )

def main():
    """Generate interactive smoke effects"""
    energy_dir = "energy_animated"
    os.makedirs(energy_dir, exist_ok=True)
    
    print("Creating interactive smoke effects...")
    
    # Create interactive smoke effects
    interactive_effects = [
        ("Interactive_Mouse_Smoke", create_mouse_following_smoke, 80),
        ("Interactive_Swirling_Smoke", create_swirling_smoke_trail, 100),
        ("Interactive_Dynamic_Burst", create_dynamic_smoke_burst, 120),
    ]
    
    for effect_name, effect_func, duration in interactive_effects:
        print(f"Creating {effect_name}...")
        frames = effect_func()
        
        # Save as GIF
        gif_path = os.path.join(energy_dir, f"{effect_name}.gif")
        save_animated_gif(frames, gif_path, duration)
        print(f"Saved: {gif_path}")
        
        # Also save first frame as PNG for static version
        png_path = os.path.join(energy_dir, f"{effect_name}.png")
        frames[0].save(png_path, 'PNG')
        print(f"Saved static: {png_path}")
    
    print(f"\nCreated {len(interactive_effects)} interactive smoke effects!")
    print("Interactive smoke effects are ready!")

if __name__ == "__main__":
    main()
