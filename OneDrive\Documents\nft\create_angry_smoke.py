#!/usr/bin/env python3
"""
Create angry/realistic smoke effects for NFT generation
"""

from PIL import Image, ImageDraw, ImageFilter, ImageEnhance
import os
import random
import math

def create_angry_steam_from_ears(size=(1000, 1000), frames=40):
    """Create angry steam coming from ears effect"""
    frame_list = []
    
    # Ear positions (left and right)
    left_ear_x, left_ear_y = 280, 350
    right_ear_x, right_ear_y = 720, 350
    
    # Generate steam particles for each ear
    steam_particles = []
    for ear_x, ear_y in [(left_ear_x, left_ear_y), (right_ear_x, right_ear_y)]:
        for _ in range(30):
            steam_particles.append({
                'birth_frame': random.randint(0, frames - 1),
                'life_span': random.randint(15, 25),
                'start_x': ear_x + random.randint(-8, 8),
                'start_y': ear_y + random.randint(-5, 5),
                'velocity_x': random.uniform(-0.5, 0.5),
                'velocity_y': random.uniform(-4, -2),
                'size': random.uniform(4, 8),
                'anger_intensity': random.uniform(0.7, 1.0),
                'ear_side': 'left' if ear_x < size[0] // 2 else 'right'
            })
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Anger pulsing effect
        anger_pulse = 0.8 + 0.2 * math.sin(frame * 0.4)
        
        # Draw steam particles
        for particle in steam_particles:
            age = frame - particle['birth_frame']
            
            if age < 0 or age >= particle['life_span']:
                continue
            
            age_factor = age / particle['life_span']
            
            # Angry steam movement - more violent
            anger_shake = math.sin(age * 0.8 + frame * 0.3) * 8 * particle['anger_intensity']
            ear_direction = -1 if particle['ear_side'] == 'left' else 1
            
            x = (particle['start_x'] + 
                 particle['velocity_x'] * age + 
                 anger_shake + 
                 ear_direction * age_factor * 10)
            
            y = (particle['start_y'] + 
                 particle['velocity_y'] * age)
            
            # Steam characteristics
            current_size = particle['size'] * (1 + age_factor * 2) * anger_pulse
            
            # Angry steam color - white with red tint
            red_tint = int(particle['anger_intensity'] * 50)
            steam_intensity = int(255 * (1 - age_factor * 0.3))
            steam_color = (steam_intensity, steam_intensity - red_tint, steam_intensity - red_tint)
            
            current_opacity = int(180 * (1 - age_factor * 0.8) * anger_pulse)
            
            if current_opacity <= 0:
                continue
            
            # Draw particle
            particle_radius = int(current_size)
            if particle_radius > 0:
                full_color = (*steam_color, current_opacity)
                
                x1, y1 = max(0, x - particle_radius), max(0, y - particle_radius)
                x2, y2 = min(size[0], x + particle_radius), min(size[1], y + particle_radius)
                
                if x2 > x1 and y2 > y1:
                    draw.ellipse([x1, y1, x2, y2], fill=full_color)
        
        img = img.filter(ImageFilter.GaussianBlur(radius=2))
        frame_list.append(img)
    
    return frame_list

def create_rage_smoke_explosion(size=(1000, 1000), frames=35):
    """Create explosive rage smoke effect"""
    frame_list = []
    
    # Explosion center
    center_x, center_y = size[0] // 2, size[1] // 2
    
    # Generate explosion particles
    explosion_particles = []
    for _ in range(80):
        angle = random.uniform(0, 2 * math.pi)
        explosion_particles.append({
            'birth_frame': random.randint(0, 10),  # Start early
            'life_span': random.randint(20, 30),
            'angle': angle,
            'speed': random.uniform(2, 6),
            'size': random.uniform(6, 15),
            'rage_level': random.uniform(0.8, 1.0),
            'spin': random.uniform(-0.3, 0.3)
        })
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Explosion intensity
        explosion_factor = max(0, 1 - frame / 15.0)  # Strong at start, fades
        
        # Draw explosion particles
        for particle in explosion_particles:
            age = frame - particle['birth_frame']
            
            if age < 0 or age >= particle['life_span']:
                continue
            
            age_factor = age / particle['life_span']
            
            # Explosive movement
            current_angle = particle['angle'] + particle['spin'] * age
            distance = particle['speed'] * age * (1 + explosion_factor)
            
            x = center_x + math.cos(current_angle) * distance
            y = center_y + math.sin(current_angle) * distance
            
            # Rage smoke characteristics
            current_size = particle['size'] * (1 + age_factor * 1.5) * (1 + explosion_factor * 0.5)
            
            # Rage color - dark with red undertones
            rage_intensity = particle['rage_level']
            if age_factor < 0.3:
                # Hot, angry smoke
                base_color = int(80 * (1 - rage_intensity * 0.5))
                smoke_color = (base_color + 30, base_color, base_color)
            else:
                # Cooling down
                base_color = int(60 + age_factor * 80)
                smoke_color = (base_color, base_color - 10, base_color - 10)
            
            current_opacity = int(160 * (1 - age_factor * 0.7) * (1 + explosion_factor * 0.3))
            
            if current_opacity <= 0:
                continue
            
            # Draw particle with jagged edges for anger
            particle_radius = int(current_size)
            if particle_radius > 0:
                full_color = (*smoke_color, current_opacity)
                
                # Create jagged/angry shape
                for spike in range(8):
                    spike_angle = spike * math.pi / 4 + frame * 0.1
                    spike_length = particle_radius * random.uniform(0.8, 1.2)
                    
                    spike_x = x + math.cos(spike_angle) * spike_length
                    spike_y = y + math.sin(spike_angle) * spike_length
                    
                    spike_size = max(2, particle_radius // 3)
                    
                    sx1, sy1 = max(0, spike_x - spike_size), max(0, spike_y - spike_size)
                    sx2, sy2 = min(size[0], spike_x + spike_size), min(size[1], spike_y + spike_size)
                    
                    if sx2 > sx1 and sy2 > sy1:
                        draw.ellipse([sx1, sy1, sx2, sy2], fill=full_color)
        
        img = img.filter(ImageFilter.GaussianBlur(radius=3))
        frame_list.append(img)
    
    return frame_list

def create_frustrated_puffing_smoke(size=(1000, 1000), frames=50):
    """Create frustrated puffing smoke effect"""
    frame_list = []
    
    # Mouth position
    mouth_x, mouth_y = size[0] // 2, int(size[1] * 0.6)
    
    # Generate puff particles
    puff_particles = []
    for puff_cycle in range(5):  # 5 puff cycles
        puff_start = puff_cycle * 10
        for _ in range(20):
            puff_particles.append({
                'birth_frame': puff_start + random.randint(0, 3),
                'life_span': random.randint(18, 28),
                'start_x': mouth_x + random.randint(-15, 15),
                'start_y': mouth_y + random.randint(-8, 8),
                'velocity_x': random.uniform(-1, 1),
                'velocity_y': random.uniform(-3, -1),
                'size': random.uniform(5, 12),
                'frustration': random.uniform(0.6, 1.0),
                'puff_cycle': puff_cycle
            })
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Frustration level increases over time
        frustration_level = min(1.0, frame / 30.0)
        
        # Draw puff particles
        for particle in puff_particles:
            age = frame - particle['birth_frame']
            
            if age < 0 or age >= particle['life_span']:
                continue
            
            age_factor = age / particle['life_span']
            
            # Frustrated movement - erratic
            frustration_shake = (math.sin(age * 0.6 + frame * 0.2) * 
                               particle['frustration'] * frustration_level * 12)
            
            x = (particle['start_x'] + 
                 particle['velocity_x'] * age + 
                 frustration_shake)
            
            y = (particle['start_y'] + 
                 particle['velocity_y'] * age)
            
            # Puff characteristics
            puff_intensity = 1.0 + particle['frustration'] * frustration_level
            current_size = particle['size'] * (1 + age_factor * 2) * puff_intensity
            
            # Frustrated smoke color - grayish with slight yellow tint
            gray_base = int(120 + particle['frustration'] * 40)
            yellow_tint = int(frustration_level * 20)
            smoke_color = (gray_base + yellow_tint, gray_base + yellow_tint//2, gray_base)
            
            current_opacity = int(140 * (1 - age_factor * 0.8) * puff_intensity)
            
            if current_opacity <= 0:
                continue
            
            # Draw particle
            particle_radius = int(current_size)
            if particle_radius > 0:
                full_color = (*smoke_color, current_opacity)
                
                x1, y1 = max(0, x - particle_radius), max(0, y - particle_radius)
                x2, y2 = min(size[0], x + particle_radius), min(size[1], y + particle_radius)
                
                if x2 > x1 and y2 > y1:
                    draw.ellipse([x1, y1, x2, y2], fill=full_color)
        
        img = img.filter(ImageFilter.GaussianBlur(radius=4))
        frame_list.append(img)
    
    return frame_list

def create_volcanic_anger_smoke(size=(1000, 1000), frames=60):
    """Create volcanic anger smoke effect"""
    frame_list = []
    
    # Volcano base
    volcano_x, volcano_y = size[0] // 2, int(size[1] * 0.8)
    
    # Generate volcanic particles
    volcanic_particles = []
    for _ in range(120):
        volcanic_particles.append({
            'birth_frame': random.randint(0, frames - 1),
            'life_span': random.randint(30, 50),
            'start_x': volcano_x + random.randint(-25, 25),
            'start_y': volcano_y + random.randint(-10, 10),
            'velocity_x': random.uniform(-1.5, 1.5),
            'velocity_y': random.uniform(-5, -2),
            'size': random.uniform(8, 18),
            'heat': random.uniform(0.7, 1.0),
            'volcanic_power': random.uniform(0.8, 1.0)
        })
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Volcanic eruption intensity
        eruption_cycle = (frame % 40) / 40.0
        eruption_power = 0.5 + 0.5 * math.sin(eruption_cycle * math.pi * 2)
        
        # Draw volcanic glow
        glow_size = int(40 * eruption_power)
        glow_color = (255, int(100 + eruption_power * 100), 0, int(100 * eruption_power))
        
        draw.ellipse([
            volcano_x - glow_size, volcano_y - glow_size//2,
            volcano_x + glow_size, volcano_y + glow_size//2
        ], fill=glow_color)
        
        # Draw volcanic particles
        for particle in volcanic_particles:
            age = frame - particle['birth_frame']
            
            if age < 0 or age >= particle['life_span']:
                continue
            
            age_factor = age / particle['life_span']
            
            # Volcanic convection
            heat_rise = particle['heat'] * age * 0.3
            volcanic_turbulence = (math.sin(age * 0.3 + frame * 0.1) * 
                                 particle['volcanic_power'] * 20)
            
            x = (particle['start_x'] + 
                 particle['velocity_x'] * age + 
                 volcanic_turbulence)
            
            y = (particle['start_y'] + 
                 particle['velocity_y'] * age - 
                 heat_rise)
            
            # Volcanic smoke characteristics
            current_size = particle['size'] * (1 + age_factor * 3) * eruption_power
            
            # Volcanic color progression
            if age_factor < 0.2:
                # Hot volcanic ash - dark red/black
                heat_factor = particle['heat']
                smoke_color = (int(60 + heat_factor * 40), int(30 + heat_factor * 20), 20)
            elif age_factor < 0.5:
                # Cooling ash - dark gray
                smoke_color = (80, 70, 60)
            else:
                # Dispersed ash - light gray
                gray_level = int(100 + age_factor * 60)
                smoke_color = (gray_level, gray_level - 10, gray_level - 20)
            
            current_opacity = int(particle['volcanic_power'] * 150 * (1 - age_factor * 0.8))
            
            if current_opacity <= 0:
                continue
            
            # Draw particle
            particle_radius = int(current_size)
            if particle_radius > 0:
                full_color = (*smoke_color, current_opacity)
                
                x1, y1 = max(0, x - particle_radius), max(0, y - particle_radius)
                x2, y2 = min(size[0], x + particle_radius), min(size[1], y + particle_radius)
                
                if x2 > x1 and y2 > y1:
                    draw.ellipse([x1, y1, x2, y2], fill=full_color)
        
        img = img.filter(ImageFilter.GaussianBlur(radius=5))
        frame_list.append(img)
    
    return frame_list

def save_animated_gif(frames, filename, duration=100):
    """Save frames as animated GIF"""
    if frames:
        frames[0].save(
            filename,
            save_all=True,
            append_images=frames[1:],
            duration=duration,
            loop=0,
            format='GIF'
        )

def main():
    """Generate angry smoke effects"""
    energy_dir = "energy_animated"
    os.makedirs(energy_dir, exist_ok=True)
    
    print("Creating angry smoke effects...")
    
    # Create angry smoke effects
    angry_effects = [
        ("Angry_Steam_From_Ears", create_angry_steam_from_ears, 150),
        ("Rage_Smoke_Explosion", create_rage_smoke_explosion, 120),
        ("Frustrated_Puffing_Smoke", create_frustrated_puffing_smoke, 100),
        ("Volcanic_Anger_Smoke", create_volcanic_anger_smoke, 130),
    ]
    
    for effect_name, effect_func, duration in angry_effects:
        print(f"Creating {effect_name}...")
        frames = effect_func()
        
        # Save as GIF
        gif_path = os.path.join(energy_dir, f"{effect_name}.gif")
        save_animated_gif(frames, gif_path, duration)
        print(f"Saved: {gif_path}")
        
        # Also save first frame as PNG for static version
        png_path = os.path.join(energy_dir, f"{effect_name}.png")
        frames[0].save(png_path, 'PNG')
        print(f"Saved static: {png_path}")
    
    print(f"\nCreated {len(angry_effects)} angry smoke effects!")
    print("Angry smoke effects are ready!")

if __name__ == "__main__":
    main()
