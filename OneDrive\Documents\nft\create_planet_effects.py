#!/usr/bin/env python3
"""
Create animated solar system planet effects for NFT generation
"""

from PIL import Image, ImageDraw, ImageFilter
import os
import random
import math

def create_animated_solar_system(size=(1000, 1000), frames=60):
    """Create animated solar system with orbiting planets"""
    frame_list = []
    
    # Sun position (center)
    sun_x, sun_y = size[0] // 2, size[1] // 2
    
    # Planet data: (name, orbit_radius, size, color, speed_multiplier)
    planets = [
        ("Mercury", 80, 8, (169, 169, 169), 4.0),    # Gray, fast
        ("Venus", 110, 12, (255, 198, 73), 3.5),     # Yellow-orange
        ("Earth", 150, 14, (100, 149, 237), 3.0),    # Blue
        ("Mars", 190, 10, (205, 92, 92), 2.5),       # Red
        ("Jupiter", 250, 25, (255, 140, 0), 1.5),    # Orange
        ("Saturn", 300, 22, (255, 215, 0), 1.2),     # Gold
        ("Uranus", 350, 18, (64, 224, 208), 0.8),    # Turquoise
        ("Neptune", 400, 16, (30, 144, 255), 0.6),   # Deep blue
    ]
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Draw orbit paths (faint)
        for _, orbit_radius, _, _, _ in planets:
            draw.ellipse([
                sun_x - orbit_radius, sun_y - orbit_radius,
                sun_x + orbit_radius, sun_y + orbit_radius
            ], outline=(100, 100, 100, 50), width=1)
        
        # Draw Sun
        sun_pulse = 1.0 + 0.1 * math.sin(frame * 0.2)
        sun_size = int(30 * sun_pulse)
        
        # Sun glow layers
        for glow_layer in range(3):
            glow_size = sun_size + glow_layer * 15
            glow_alpha = 150 - glow_layer * 40
            sun_color = (255, 255, 0, glow_alpha)
            
            draw.ellipse([
                sun_x - glow_size, sun_y - glow_size,
                sun_x + glow_size, sun_y + glow_size
            ], fill=sun_color)
        
        # Sun core
        draw.ellipse([
            sun_x - sun_size//2, sun_y - sun_size//2,
            sun_x + sun_size//2, sun_y + sun_size//2
        ], fill=(255, 255, 255, 255))
        
        # Draw planets
        for planet_name, orbit_radius, planet_size, planet_color, speed in planets:
            # Calculate planet position
            angle = (frame * speed * 0.05) % (2 * math.pi)
            planet_x = sun_x + math.cos(angle) * orbit_radius
            planet_y = sun_y + math.sin(angle) * orbit_radius
            
            # Planet glow
            glow_size = planet_size + 8
            glow_color = (*planet_color, 80)
            draw.ellipse([
                planet_x - glow_size, planet_y - glow_size,
                planet_x + glow_size, planet_y + glow_size
            ], fill=glow_color)
            
            # Planet body
            planet_alpha = 220
            body_color = (*planet_color, planet_alpha)
            draw.ellipse([
                planet_x - planet_size, planet_y - planet_size,
                planet_x + planet_size, planet_y + planet_size
            ], fill=body_color)
            
            # Special effects for certain planets
            if planet_name == "Saturn":
                # Saturn's rings
                ring_inner = planet_size + 5
                ring_outer = planet_size + 12
                ring_color = (200, 200, 150, 120)
                
                # Draw ring segments
                for ring_segment in range(0, 360, 10):
                    ring_angle = math.radians(ring_segment)
                    inner_x = planet_x + math.cos(ring_angle) * ring_inner
                    inner_y = planet_y + math.sin(ring_angle) * ring_inner * 0.3
                    outer_x = planet_x + math.cos(ring_angle) * ring_outer
                    outer_y = planet_y + math.sin(ring_angle) * ring_outer * 0.3
                    
                    draw.line([(inner_x, inner_y), (outer_x, outer_y)], 
                            fill=ring_color, width=2)
            
            elif planet_name == "Earth":
                # Earth's moon
                moon_angle = angle * 12  # Moon orbits faster
                moon_distance = planet_size + 20
                moon_x = planet_x + math.cos(moon_angle) * moon_distance
                moon_y = planet_y + math.sin(moon_angle) * moon_distance
                
                moon_size = 4
                draw.ellipse([
                    moon_x - moon_size, moon_y - moon_size,
                    moon_x + moon_size, moon_y + moon_size
                ], fill=(200, 200, 200, 180))
        
        # Add some stars in background
        if frame == 0:  # Only calculate once
            star_positions = []
            for _ in range(50):
                star_x = random.randint(0, size[0])
                star_y = random.randint(0, size[1])
                star_size = random.randint(1, 3)
                star_positions.append((star_x, star_y, star_size))
        
        # Draw twinkling stars
        for star_x, star_y, star_size in star_positions if 'star_positions' in locals() else []:
            twinkle = 0.5 + 0.5 * math.sin(frame * 0.1 + star_x * 0.01)
            star_alpha = int(150 * twinkle)
            star_color = (255, 255, 255, star_alpha)
            
            draw.ellipse([
                star_x - star_size, star_y - star_size,
                star_x + star_size, star_y + star_size
            ], fill=star_color)
        
        img = img.filter(ImageFilter.GaussianBlur(radius=1))
        frame_list.append(img)
    
    return frame_list

def create_animated_planet_rings(size=(1000, 1000), frames=40):
    """Create animated planet with rotating rings"""
    frame_list = []
    
    center_x, center_y = size[0] // 2, size[1] // 2
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Planet body
        planet_size = 80
        planet_color = (255, 215, 0, 200)  # Gold
        
        # Planet glow
        for glow_layer in range(4):
            glow_size = planet_size + glow_layer * 20
            glow_alpha = 100 - glow_layer * 20
            if glow_alpha > 0:
                draw.ellipse([
                    center_x - glow_size, center_y - glow_size,
                    center_x + glow_size, center_y + glow_size
                ], fill=(*planet_color[:3], glow_alpha))
        
        # Planet core
        draw.ellipse([
            center_x - planet_size, center_y - planet_size,
            center_x + planet_size, center_y + planet_size
        ], fill=planet_color)
        
        # Rotating rings
        ring_rotation = frame * 2
        
        # Multiple ring layers
        rings = [
            (120, 140, (200, 200, 150, 100)),
            (150, 170, (180, 180, 130, 80)),
            (180, 200, (160, 160, 110, 60)),
        ]
        
        for ring_inner, ring_outer, ring_color in rings:
            # Draw ring particles
            for particle in range(0, 360, 3):
                particle_angle = math.radians(particle + ring_rotation)
                
                # Vary ring density
                if random.random() > 0.3:
                    ring_radius = random.uniform(ring_inner, ring_outer)
                    particle_x = center_x + math.cos(particle_angle) * ring_radius
                    particle_y = center_y + math.sin(particle_angle) * ring_radius * 0.2
                    
                    particle_size = random.randint(1, 3)
                    draw.ellipse([
                        particle_x - particle_size, particle_y - particle_size,
                        particle_x + particle_size, particle_y + particle_size
                    ], fill=ring_color)
        
        img = img.filter(ImageFilter.GaussianBlur(radius=2))
        frame_list.append(img)
    
    return frame_list

def create_animated_gas_giant(size=(1000, 1000), frames=50):
    """Create animated gas giant with swirling atmosphere"""
    frame_list = []
    
    center_x, center_y = size[0] // 2, size[1] // 2
    planet_radius = 100
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Gas giant atmospheric bands
        band_colors = [
            (255, 140, 0, 180),   # Orange
            (255, 165, 0, 160),   # Orange-yellow
            (255, 200, 100, 140), # Light orange
            (200, 100, 50, 120),  # Brown
        ]
        
        # Draw swirling atmospheric bands
        for band_idx, band_color in enumerate(band_colors):
            band_y_offset = (band_idx - 1.5) * 40
            
            # Create wavy band
            for x in range(-planet_radius, planet_radius, 5):
                # Calculate if point is within planet circle
                for y_offset in range(-15, 16, 3):
                    y = band_y_offset + y_offset
                    
                    # Check if within planet bounds
                    if x*x + y*y <= planet_radius*planet_radius:
                        # Add swirling motion
                        wave_offset = math.sin((x + frame * 2) * 0.05) * 10
                        swirl_offset = math.cos((y + frame * 1.5) * 0.03) * 5
                        
                        draw_x = center_x + x + wave_offset
                        draw_y = center_y + y + swirl_offset
                        
                        # Vary band thickness
                        band_thickness = random.randint(2, 6)
                        draw.ellipse([
                            draw_x - band_thickness, draw_y - band_thickness,
                            draw_x + band_thickness, draw_y + band_thickness
                        ], fill=band_color)
        
        # Great Red Spot (Jupiter-like storm)
        spot_x = center_x + 30
        spot_y = center_y - 20
        spot_size_x = 25 + int(5 * math.sin(frame * 0.1))
        spot_size_y = 15 + int(3 * math.cos(frame * 0.15))
        
        draw.ellipse([
            spot_x - spot_size_x, spot_y - spot_size_y,
            spot_x + spot_size_x, spot_y + spot_size_y
        ], fill=(200, 50, 50, 160))
        
        # Planet glow
        glow_size = planet_radius + 30
        draw.ellipse([
            center_x - glow_size, center_y - glow_size,
            center_x + glow_size, center_y + glow_size
        ], fill=(255, 140, 0, 40))
        
        img = img.filter(ImageFilter.GaussianBlur(radius=3))
        frame_list.append(img)
    
    return frame_list

def create_animated_asteroid_belt(size=(1000, 1000), frames=80):
    """Create animated asteroid belt"""
    frame_list = []
    
    center_x, center_y = size[0] // 2, size[1] // 2
    
    # Generate asteroid positions
    asteroids = []
    for _ in range(100):
        asteroids.append({
            'distance': random.uniform(200, 350),
            'angle_offset': random.uniform(0, 2 * math.pi),
            'size': random.randint(2, 8),
            'speed': random.uniform(0.5, 2.0),
            'color_variant': random.randint(0, 2)
        })
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Draw asteroids
        for asteroid in asteroids:
            angle = asteroid['angle_offset'] + frame * asteroid['speed'] * 0.02
            
            x = center_x + math.cos(angle) * asteroid['distance']
            y = center_y + math.sin(angle) * asteroid['distance']
            
            # Asteroid colors
            colors = [
                (139, 69, 19, 180),   # Brown
                (105, 105, 105, 180), # Gray
                (160, 82, 45, 180),   # Saddle brown
            ]
            
            asteroid_color = colors[asteroid['color_variant']]
            asteroid_size = asteroid['size']
            
            # Draw irregular asteroid shape
            for i in range(6):
                offset_angle = i * math.pi / 3
                offset_x = math.cos(offset_angle) * asteroid_size * random.uniform(0.7, 1.3)
                offset_y = math.sin(offset_angle) * asteroid_size * random.uniform(0.7, 1.3)
                
                draw.ellipse([
                    x + offset_x - 2, y + offset_y - 2,
                    x + offset_x + 2, y + offset_y + 2
                ], fill=asteroid_color)
        
        # Add some larger asteroids with special effects
        if frame % 20 == 0:
            # Asteroid collision spark
            spark_x = random.randint(200, 800)
            spark_y = random.randint(200, 800)
            
            for _ in range(5):
                spark_offset_x = random.randint(-10, 10)
                spark_offset_y = random.randint(-10, 10)
                
                draw.ellipse([
                    spark_x + spark_offset_x - 3, spark_y + spark_offset_y - 3,
                    spark_x + spark_offset_x + 3, spark_y + spark_offset_y + 3
                ], fill=(255, 255, 0, 200))
        
        img = img.filter(ImageFilter.GaussianBlur(radius=1))
        frame_list.append(img)
    
    return frame_list

def create_animated_nebula_planets(size=(1000, 1000), frames=70):
    """Create animated planets in colorful nebula"""
    frame_list = []
    
    center_x, center_y = size[0] // 2, size[1] // 2
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Nebula background
        nebula_colors = [
            (255, 100, 200, 30),  # Pink
            (100, 150, 255, 25),  # Blue
            (200, 100, 255, 20),  # Purple
        ]
        
        for color_idx, nebula_color in enumerate(nebula_colors):
            for _ in range(20):
                nebula_x = random.randint(0, size[0])
                nebula_y = random.randint(0, size[1])
                nebula_size = random.randint(30, 80)
                
                # Animated nebula movement
                wave_x = nebula_x + math.sin(frame * 0.02 + color_idx) * 20
                wave_y = nebula_y + math.cos(frame * 0.015 + color_idx) * 15
                
                draw.ellipse([
                    wave_x - nebula_size, wave_y - nebula_size,
                    wave_x + nebula_size, wave_y + nebula_size
                ], fill=nebula_color)
        
        # Exotic planets
        planets = [
            (center_x - 150, center_y - 100, 25, (255, 0, 255, 200)),  # Magenta
            (center_x + 200, center_y + 50, 30, (0, 255, 255, 200)),   # Cyan
            (center_x - 50, center_y + 150, 20, (255, 255, 0, 200)),   # Yellow
        ]
        
        for planet_x, planet_y, planet_size, planet_color in planets:
            # Planet rotation effect
            rotation = frame * 0.1
            
            # Planet glow
            glow_size = planet_size + 15
            glow_color = (*planet_color[:3], 60)
            draw.ellipse([
                planet_x - glow_size, planet_y - glow_size,
                planet_x + glow_size, planet_y + glow_size
            ], fill=glow_color)
            
            # Planet body
            draw.ellipse([
                planet_x - planet_size, planet_y - planet_size,
                planet_x + planet_size, planet_y + planet_size
            ], fill=planet_color)
            
            # Planet surface patterns
            for pattern in range(8):
                pattern_angle = rotation + pattern * math.pi / 4
                pattern_x = planet_x + math.cos(pattern_angle) * planet_size * 0.7
                pattern_y = planet_y + math.sin(pattern_angle) * planet_size * 0.7
                
                pattern_size = 3
                pattern_color = (255, 255, 255, 100)
                draw.ellipse([
                    pattern_x - pattern_size, pattern_y - pattern_size,
                    pattern_x + pattern_size, pattern_y + pattern_size
                ], fill=pattern_color)
        
        img = img.filter(ImageFilter.GaussianBlur(radius=4))
        frame_list.append(img)
    
    return frame_list

def save_animated_gif(frames, filename, duration=100):
    """Save frames as animated GIF"""
    if frames:
        frames[0].save(
            filename,
            save_all=True,
            append_images=frames[1:],
            duration=duration,
            loop=0,
            format='GIF'
        )

def main():
    """Generate animated planet effects"""
    energy_dir = "energy_animated"
    os.makedirs(energy_dir, exist_ok=True)
    
    print("Creating animated planet effects...")
    
    # Create planet effects
    planet_effects = [
        ("Animated_Solar_System", create_animated_solar_system, 150),
        ("Animated_Planet_Rings", create_animated_planet_rings, 120),
        ("Animated_Gas_Giant", create_animated_gas_giant, 100),
        ("Animated_Asteroid_Belt", create_animated_asteroid_belt, 80),
        ("Animated_Nebula_Planets", create_animated_nebula_planets, 110),
    ]
    
    for effect_name, effect_func, duration in planet_effects:
        print(f"Creating {effect_name}...")
        frames = effect_func()
        
        # Save as GIF
        gif_path = os.path.join(energy_dir, f"{effect_name}.gif")
        save_animated_gif(frames, gif_path, duration)
        print(f"Saved: {gif_path}")
        
        # Also save first frame as PNG for static version
        png_path = os.path.join(energy_dir, f"{effect_name}.png")
        frames[0].save(png_path, 'PNG')
        print(f"Saved static: {png_path}")
    
    print(f"\nCreated {len(planet_effects)} animated planet effects!")
    print("Planet effects are ready!")

if __name__ == "__main__":
    main()
