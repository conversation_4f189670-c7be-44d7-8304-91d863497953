#!/usr/bin/env python3
"""
Create animated eye effect images for NFT generation
"""

from PIL import Image, ImageDraw, ImageFilter
import os
import random
import math

def create_animated_blinking_eyes(size=(1000, 1000), frames=30):
    """Create animated blinking eyes effect"""
    frame_list = []
    
    # Eye positions (left and right)
    left_eye_x, left_eye_y = 350, 400
    right_eye_x, right_eye_y = 650, 400
    eye_width, eye_height = 80, 40
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Blinking animation cycle
        blink_cycle = frame % 20
        
        if blink_cycle < 15:  # Eyes open
            eye_openness = 1.0
        elif blink_cycle < 17:  # Closing
            eye_openness = 0.3
        elif blink_cycle < 18:  # Closed
            eye_openness = 0.05
        else:  # Opening
            eye_openness = 0.6
        
        current_eye_height = int(eye_height * eye_openness)
        
        # Draw left eye
        if current_eye_height > 2:
            # Eye white
            draw.ellipse([
                left_eye_x - eye_width//2, left_eye_y - current_eye_height//2,
                left_eye_x + eye_width//2, left_eye_y + current_eye_height//2
            ], fill=(255, 255, 255, 200))
            
            # Pupil
            pupil_size = int(current_eye_height * 0.6)
            draw.ellipse([
                left_eye_x - pupil_size//2, left_eye_y - pupil_size//2,
                left_eye_x + pupil_size//2, left_eye_y + pupil_size//2
            ], fill=(0, 0, 0, 255))
            
            # Iris glow
            iris_glow = int(current_eye_height * 0.3)
            draw.ellipse([
                left_eye_x - iris_glow//2, left_eye_y - iris_glow//2,
                left_eye_x + iris_glow//2, left_eye_y + iris_glow//2
            ], fill=(0, 150, 255, 150))
        
        # Draw right eye
        if current_eye_height > 2:
            # Eye white
            draw.ellipse([
                right_eye_x - eye_width//2, right_eye_y - current_eye_height//2,
                right_eye_x + eye_width//2, right_eye_y + current_eye_height//2
            ], fill=(255, 255, 255, 200))
            
            # Pupil
            pupil_size = int(current_eye_height * 0.6)
            draw.ellipse([
                right_eye_x - pupil_size//2, right_eye_y - pupil_size//2,
                right_eye_x + pupil_size//2, right_eye_y + pupil_size//2
            ], fill=(0, 0, 0, 255))
            
            # Iris glow
            iris_glow = int(current_eye_height * 0.3)
            draw.ellipse([
                right_eye_x - iris_glow//2, right_eye_y - iris_glow//2,
                right_eye_x + iris_glow//2, right_eye_y + iris_glow//2
            ], fill=(0, 150, 255, 150))
        
        # Add subtle glow around eyes when open
        if eye_openness > 0.5:
            glow_radius = int(eye_width * 0.8)
            glow_alpha = int(30 * eye_openness)
            
            # Left eye glow
            draw.ellipse([
                left_eye_x - glow_radius, left_eye_y - glow_radius//2,
                left_eye_x + glow_radius, left_eye_y + glow_radius//2
            ], fill=(100, 200, 255, glow_alpha))
            
            # Right eye glow
            draw.ellipse([
                right_eye_x - glow_radius, right_eye_y - glow_radius//2,
                right_eye_x + glow_radius, right_eye_y + glow_radius//2
            ], fill=(100, 200, 255, glow_alpha))
        
        img = img.filter(ImageFilter.GaussianBlur(radius=2))
        frame_list.append(img)
    
    return frame_list

def create_animated_laser_eyes(size=(1000, 1000), frames=25):
    """Create animated laser eyes effect"""
    frame_list = []
    
    # Eye positions
    left_eye_x, left_eye_y = 350, 400
    right_eye_x, right_eye_y = 650, 400
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Laser intensity animation
        laser_intensity = 0.5 + 0.5 * math.sin(frame * 0.3)
        
        # Draw laser beams
        beam_width = int(15 + 10 * laser_intensity)
        beam_alpha = int(150 + 100 * laser_intensity)
        
        # Left laser beam
        beam_color = (255, 0, 0, beam_alpha)
        for i in range(beam_width):
            y_offset = left_eye_y + i - beam_width//2
            draw.line([
                (left_eye_x, y_offset),
                (size[0], y_offset + random.randint(-5, 5))
            ], fill=beam_color, width=2)
        
        # Right laser beam
        for i in range(beam_width):
            y_offset = right_eye_y + i - beam_width//2
            draw.line([
                (right_eye_x, y_offset),
                (size[0], y_offset + random.randint(-5, 5))
            ], fill=beam_color, width=2)
        
        # Eye glow sources
        eye_glow_size = int(40 + 20 * laser_intensity)
        eye_glow_alpha = int(200 + 50 * laser_intensity)
        
        # Left eye glow
        draw.ellipse([
            left_eye_x - eye_glow_size, left_eye_y - eye_glow_size//2,
            left_eye_x + eye_glow_size, left_eye_y + eye_glow_size//2
        ], fill=(255, 100, 100, eye_glow_alpha))
        
        # Right eye glow
        draw.ellipse([
            right_eye_x - eye_glow_size, right_eye_y - eye_glow_size//2,
            right_eye_x + eye_glow_size, right_eye_y + eye_glow_size//2
        ], fill=(255, 100, 100, eye_glow_alpha))
        
        # Core laser points
        core_size = int(15 + 10 * laser_intensity)
        draw.ellipse([
            left_eye_x - core_size//2, left_eye_y - core_size//2,
            left_eye_x + core_size//2, left_eye_y + core_size//2
        ], fill=(255, 255, 255, 255))
        
        draw.ellipse([
            right_eye_x - core_size//2, right_eye_y - core_size//2,
            right_eye_x + core_size//2, right_eye_y + core_size//2
        ], fill=(255, 255, 255, 255))
        
        img = img.filter(ImageFilter.GaussianBlur(radius=3))
        frame_list.append(img)
    
    return frame_list

def create_animated_glowing_eyes(size=(1000, 1000), frames=40):
    """Create animated glowing eyes effect"""
    frame_list = []
    
    # Eye positions
    left_eye_x, left_eye_y = 350, 400
    right_eye_x, right_eye_y = 650, 400
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Glow pulsing animation
        glow_pulse = 0.3 + 0.7 * (math.sin(frame * 0.2) + 1) / 2
        
        # Color cycling
        color_cycle = frame % 60
        if color_cycle < 20:
            base_color = (0, 255, 255)  # Cyan
        elif color_cycle < 40:
            base_color = (255, 0, 255)  # Magenta
        else:
            base_color = (255, 255, 0)  # Yellow
        
        # Draw multiple glow layers
        for layer in range(5):
            layer_size = int((60 - layer * 8) * glow_pulse)
            layer_alpha = int((100 - layer * 15) * glow_pulse)
            
            if layer_alpha > 0 and layer_size > 0:
                glow_color = (*base_color, layer_alpha)
                
                # Left eye glow
                draw.ellipse([
                    left_eye_x - layer_size, left_eye_y - layer_size//2,
                    left_eye_x + layer_size, left_eye_y + layer_size//2
                ], fill=glow_color)
                
                # Right eye glow
                draw.ellipse([
                    right_eye_x - layer_size, right_eye_y - layer_size//2,
                    right_eye_x + layer_size, right_eye_y + layer_size//2
                ], fill=glow_color)
        
        # Eye cores
        core_size = int(20 * glow_pulse)
        core_color = (*base_color, 255)
        
        draw.ellipse([
            left_eye_x - core_size//2, left_eye_y - core_size//2,
            left_eye_x + core_size//2, left_eye_y + core_size//2
        ], fill=core_color)
        
        draw.ellipse([
            right_eye_x - core_size//2, right_eye_y - core_size//2,
            right_eye_x + core_size//2, right_eye_y + core_size//2
        ], fill=core_color)
        
        img = img.filter(ImageFilter.GaussianBlur(radius=4))
        frame_list.append(img)
    
    return frame_list

def create_animated_cybernetic_eyes(size=(1000, 1000), frames=35):
    """Create animated cybernetic eyes effect"""
    frame_list = []
    
    # Eye positions
    left_eye_x, left_eye_y = 350, 400
    right_eye_x, right_eye_y = 650, 400
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Scanning animation
        scan_progress = (frame % 20) / 20.0
        
        # Draw cybernetic eye frames
        frame_color = (100, 255, 200, 180)
        
        # Left eye frame
        draw.ellipse([
            left_eye_x - 50, left_eye_y - 25,
            left_eye_x + 50, left_eye_y + 25
        ], outline=frame_color, width=3)
        
        # Right eye frame
        draw.ellipse([
            right_eye_x - 50, right_eye_y - 25,
            right_eye_x + 50, right_eye_y + 25
        ], outline=frame_color, width=3)
        
        # Scanning lines
        scan_y = left_eye_y - 25 + int(50 * scan_progress)
        scan_color = (0, 255, 150, 200)
        
        # Left eye scan
        draw.line([
            (left_eye_x - 45, scan_y),
            (left_eye_x + 45, scan_y)
        ], fill=scan_color, width=2)
        
        # Right eye scan
        draw.line([
            (right_eye_x - 45, scan_y),
            (right_eye_x + 45, scan_y)
        ], fill=scan_color, width=2)
        
        # Eye centers with data patterns
        center_size = 15
        center_color = (0, 255, 255, 220)
        
        draw.ellipse([
            left_eye_x - center_size, left_eye_y - center_size,
            left_eye_x + center_size, left_eye_y + center_size
        ], fill=center_color)
        
        draw.ellipse([
            right_eye_x - center_size, right_eye_y - center_size,
            right_eye_x + center_size, right_eye_y + center_size
        ], fill=center_color)
        
        # Data streams
        if frame % 5 == 0:
            for _ in range(3):
                stream_x = random.randint(left_eye_x - 40, left_eye_x + 40)
                stream_y = random.randint(left_eye_y - 20, left_eye_y + 20)
                stream_size = random.randint(2, 6)
                
                draw.rectangle([
                    stream_x, stream_y,
                    stream_x + stream_size, stream_y + stream_size
                ], fill=(0, 255, 100, 150))
                
                stream_x = random.randint(right_eye_x - 40, right_eye_x + 40)
                stream_y = random.randint(right_eye_y - 20, right_eye_y + 20)
                
                draw.rectangle([
                    stream_x, stream_y,
                    stream_x + stream_size, stream_y + stream_size
                ], fill=(0, 255, 100, 150))
        
        img = img.filter(ImageFilter.GaussianBlur(radius=1))
        frame_list.append(img)
    
    return frame_list

def create_animated_hypnotic_eyes(size=(1000, 1000), frames=50):
    """Create animated hypnotic spiral eyes effect"""
    frame_list = []
    
    # Eye positions
    left_eye_x, left_eye_y = 350, 400
    right_eye_x, right_eye_y = 650, 400
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Spiral rotation
        rotation = frame * 0.2
        
        # Draw hypnotic spirals
        for eye_x, eye_y in [(left_eye_x, left_eye_y), (right_eye_x, right_eye_y)]:
            # Multiple spiral rings
            for ring in range(5):
                ring_radius = 15 + ring * 8
                ring_alpha = 150 - ring * 20
                
                if ring_alpha > 0:
                    # Create spiral points
                    spiral_points = []
                    for angle in range(0, 360, 10):
                        spiral_angle = math.radians(angle + rotation + ring * 30)
                        spiral_radius = ring_radius + math.sin(spiral_angle * 3) * 5
                        
                        x = eye_x + math.cos(spiral_angle) * spiral_radius
                        y = eye_y + math.sin(spiral_angle) * spiral_radius
                        spiral_points.append((x, y))
                    
                    # Draw spiral
                    for i in range(len(spiral_points) - 1):
                        color_intensity = int(255 * (1 - ring * 0.15))
                        spiral_color = (color_intensity, 100, 255, ring_alpha)
                        
                        draw.line([spiral_points[i], spiral_points[i + 1]], 
                                fill=spiral_color, width=2)
            
            # Eye center
            center_pulse = 0.5 + 0.5 * math.sin(frame * 0.3)
            center_size = int(10 + 5 * center_pulse)
            center_color = (255, 255, 255, int(200 * center_pulse))
            
            draw.ellipse([
                eye_x - center_size, eye_y - center_size,
                eye_x + center_size, eye_y + center_size
            ], fill=center_color)
        
        img = img.filter(ImageFilter.GaussianBlur(radius=2))
        frame_list.append(img)
    
    return frame_list

def save_animated_gif(frames, filename, duration=100):
    """Save frames as animated GIF"""
    if frames:
        frames[0].save(
            filename,
            save_all=True,
            append_images=frames[1:],
            duration=duration,
            loop=0,
            format='GIF'
        )

def main():
    """Generate animated eye effects"""
    energy_dir = "energy_animated"
    os.makedirs(energy_dir, exist_ok=True)
    
    print("Creating animated eye effects...")
    
    # Create eye effects
    eye_effects = [
        ("Animated_Blinking_Eyes", create_animated_blinking_eyes, 150),
        ("Animated_Laser_Eyes", create_animated_laser_eyes, 120),
        ("Animated_Glowing_Eyes", create_animated_glowing_eyes, 100),
        ("Animated_Cybernetic_Eyes", create_animated_cybernetic_eyes, 130),
        ("Animated_Hypnotic_Eyes", create_animated_hypnotic_eyes, 80),
    ]
    
    for effect_name, effect_func, duration in eye_effects:
        print(f"Creating {effect_name}...")
        frames = effect_func()
        
        # Save as GIF
        gif_path = os.path.join(energy_dir, f"{effect_name}.gif")
        save_animated_gif(frames, gif_path, duration)
        print(f"Saved: {gif_path}")
        
        # Also save first frame as PNG for static version
        png_path = os.path.join(energy_dir, f"{effect_name}.png")
        frames[0].save(png_path, 'PNG')
        print(f"Saved static: {png_path}")
    
    print(f"\nCreated {len(eye_effects)} animated eye effects!")
    print("Eye effects are ready!")

if __name__ == "__main__":
    main()
