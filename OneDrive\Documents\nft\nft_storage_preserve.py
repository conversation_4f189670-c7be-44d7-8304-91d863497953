#!/usr/bin/env python3
"""
Upload CyberSouls Genesis NFT collection to NFT.Storage Preserve API
"""

import requests
import os
import json
import time
import csv

# NFT.Storage Preserve API configuration
API_KEY = "67cf20a6.114f474e9bdb4c22baa2d024dfb33660"
BASE_URL = "https://preserve.nft.storage/api/v1"

# Collection details
COLLECTION_NAME = "CyberSouls Genesis"
CONTRACT_ADDRESS = "******************************************"  # Placeholder for now
CHAIN_ID = "1"  # Ethereum mainnet
NETWORK = "Ethereum"

def check_api_key():
    """Check if API key is set"""
    if not API_KEY or API_KEY == "YOUR_API_KEY_HERE":
        print("❌ ERROR: Please set your NFT.Storage API key!")
        return False
    return True

def get_user_balance():
    """Get user balance"""
    url = f"{BASE_URL}/user/get_balance"
    headers = {"Authorization": f"Bearer {API_KEY}"}
    
    try:
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            balance = response.json()
            print(f"💰 User balance: {balance}")
            return balance
        else:
            print(f"⚠️ Could not get balance: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Error getting balance: {e}")
        return None

def list_collections():
    """List existing collections"""
    url = f"{BASE_URL}/collection/list_collections"
    headers = {"Authorization": f"Bearer {API_KEY}"}
    
    try:
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            collections = response.json()
            print(f"📋 Found {len(collections)} existing collections")
            return collections
        else:
            print(f"⚠️ Could not list collections: {response.text}")
            return []
    except Exception as e:
        print(f"❌ Error listing collections: {e}")
        return []

def create_collection():
    """Create a new collection"""
    url = f"{BASE_URL}/collection/create_collection"
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    data = {
        "collectionName": COLLECTION_NAME,
        "contractAddress": CONTRACT_ADDRESS,
        "chainID": CHAIN_ID,
        "network": NETWORK
    }
    
    try:
        response = requests.post(url, headers=headers, json=data)
        if response.status_code == 200:
            print(f"✅ Collection '{COLLECTION_NAME}' created successfully!")
            return True
        else:
            print(f"❌ Error creating collection: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Exception creating collection: {e}")
        return False

def upload_file_to_nft_storage(file_path, file_name):
    """Upload a single file to NFT.Storage (original API)"""
    url = "https://api.nft.storage/upload"
    headers = {"Authorization": f"Bearer {API_KEY}"}
    
    try:
        with open(file_path, "rb") as f:
            files = {"file": (file_name, f)}
            response = requests.post(url, headers=headers, files=files)
            
            if response.status_code == 200:
                result = response.json()
                cid = result["value"]["cid"]
                print(f"✅ {file_name} → ipfs://{cid}")
                return cid
            else:
                print(f"❌ Error uploading {file_name}: {response.text}")
                return None
                
    except Exception as e:
        print(f"❌ Exception uploading {file_name}: {e}")
        return None

def upload_images():
    """Upload all images and return CID mapping"""
    images_folder = "output/images"
    
    if not os.path.exists(images_folder):
        print(f"❌ Images folder not found: {images_folder}")
        return {}
    
    print(f"\n🚀 Uploading images from: {images_folder}")
    
    image_cids = {}
    files = sorted([f for f in os.listdir(images_folder) if f.endswith('.png')])
    
    for i, file_name in enumerate(files, 1):
        file_path = os.path.join(images_folder, file_name)
        
        print(f"📤 {i}/{len(files)}: {file_name}")
        cid = upload_file_to_nft_storage(file_path, file_name)
        
        if cid:
            image_cids[file_name] = cid
        
        # Rate limiting
        time.sleep(0.3)
        
        # Progress update
        if i % 100 == 0:
            print(f"🎯 Progress: {i}/{len(files)} images uploaded")
    
    print(f"✅ Images upload complete: {len(image_cids)}/{len(files)} files")
    return image_cids

def update_metadata_with_ipfs(image_cids):
    """Update metadata files with IPFS image URLs"""
    metadata_folder = "output/metadata"
    
    print(f"\n🔄 Updating metadata with IPFS URLs...")
    
    updated_count = 0
    
    for i in range(1, 1001):
        metadata_file = f"{i}.json"
        metadata_path = os.path.join(metadata_folder, metadata_file)
        
        if os.path.exists(metadata_path):
            try:
                with open(metadata_path, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                
                image_file = f"{i}.png"
                if image_file in image_cids:
                    metadata["image"] = f"ipfs://{image_cids[image_file]}"
                    
                    with open(metadata_path, 'w', encoding='utf-8') as f:
                        json.dump(metadata, f, indent=2, ensure_ascii=False)
                    
                    updated_count += 1
                        
            except Exception as e:
                print(f"❌ Error updating NFT #{i}: {e}")
    
    print(f"✅ Updated {updated_count} metadata files with IPFS URLs")

def upload_metadata():
    """Upload all metadata files and return CID mapping"""
    metadata_folder = "output/metadata"
    
    if not os.path.exists(metadata_folder):
        print(f"❌ Metadata folder not found: {metadata_folder}")
        return {}
    
    print(f"\n🚀 Uploading metadata from: {metadata_folder}")
    
    metadata_cids = {}
    files = sorted([f for f in os.listdir(metadata_folder) if f.endswith('.json')])
    
    for i, file_name in enumerate(files, 1):
        file_path = os.path.join(metadata_folder, file_name)
        
        print(f"📤 {i}/{len(files)}: {file_name}")
        cid = upload_file_to_nft_storage(file_path, file_name)
        
        if cid:
            metadata_cids[file_name] = cid
        
        # Rate limiting
        time.sleep(0.3)
        
        # Progress update
        if i % 100 == 0:
            print(f"🎯 Progress: {i}/{len(files)} metadata uploaded")
    
    print(f"✅ Metadata upload complete: {len(metadata_cids)}/{len(files)} files")
    return metadata_cids

def create_tokens_csv(metadata_cids):
    """Create CSV file for token upload to collection"""
    csv_file = "output/tokens.csv"
    
    print(f"\n📄 Creating tokens CSV file...")
    
    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        
        # Write header
        writer.writerow(['tokenID', 'cid'])
        
        # Write token data
        for i in range(1, 1001):
            metadata_file = f"{i}.json"
            if metadata_file in metadata_cids:
                writer.writerow([str(i), metadata_cids[metadata_file]])
    
    print(f"✅ Created CSV file: {csv_file}")
    return csv_file

def add_tokens_to_collection(collection_id, csv_file):
    """Add tokens to collection using CSV file"""
    url = f"{BASE_URL}/collection/add_tokens"
    headers = {"Authorization": f"Bearer {API_KEY}"}
    
    try:
        with open(csv_file, 'rb') as f:
            files = {'file': (csv_file, f, 'text/csv')}
            data = {'collectionID': collection_id}
            
            response = requests.post(url, headers=headers, files=files, data=data)
            
            if response.status_code == 200:
                print(f"✅ Tokens added to collection successfully!")
                return True
            else:
                print(f"❌ Error adding tokens: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Exception adding tokens: {e}")
        return False

def save_results(image_cids, metadata_cids):
    """Save upload results"""
    results = {
        "collection": COLLECTION_NAME,
        "upload_service": "NFT.Storage Preserve API",
        "upload_date": time.strftime("%Y-%m-%d %H:%M:%S"),
        "contract_address": CONTRACT_ADDRESS,
        "chain_id": CHAIN_ID,
        "network": NETWORK,
        "total_images": len(image_cids),
        "total_metadata": len(metadata_cids),
        "image_cids": image_cids,
        "metadata_cids": metadata_cids,
        "ipfs_gateways": [
            "https://nftstorage.link/ipfs/",
            "https://ipfs.io/ipfs/",
            "https://gateway.pinata.cloud/ipfs/",
            "https://cloudflare-ipfs.com/ipfs/"
        ]
    }
    
    with open("output/nft_storage_results.json", "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print("💾 Results saved to: output/nft_storage_results.json")

def main():
    """Main function"""
    print("🌟 CyberSouls Genesis - NFT.Storage Preserve Upload")
    print("="*55)
    
    # Check API key
    if not check_api_key():
        return
    
    # Get user balance
    print("\n💰 STEP 1: Checking User Balance")
    get_user_balance()
    
    # List existing collections
    print("\n📋 STEP 2: Listing Collections")
    collections = list_collections()
    
    # Create collection
    print("\n🏗️ STEP 3: Creating Collection")
    if not create_collection():
        print("❌ Failed to create collection. Stopping.")
        return
    
    # Upload images
    print("\n📸 STEP 4: Uploading Images")
    image_cids = upload_images()
    
    if not image_cids:
        print("❌ No images uploaded. Stopping.")
        return
    
    # Update metadata
    print("\n📝 STEP 5: Updating Metadata")
    update_metadata_with_ipfs(image_cids)
    
    # Upload metadata
    print("\n📄 STEP 6: Uploading Metadata")
    metadata_cids = upload_metadata()
    
    # Save results
    print("\n💾 STEP 7: Saving Results")
    save_results(image_cids, metadata_cids)
    
    # Summary
    print("\n" + "="*55)
    print("🎉 NFT.STORAGE UPLOAD COMPLETE!")
    print(f"📊 Images: {len(image_cids)}")
    print(f"📊 Metadata: {len(metadata_cids)}")
    print(f"🌐 Collection: {COLLECTION_NAME}")
    
    if image_cids:
        first_image = list(image_cids.keys())[0]
        print(f"\n🔗 Example: ipfs://{image_cids[first_image]}")
        print(f"🌐 Gateway: https://nftstorage.link/ipfs/{image_cids[first_image]}")

if __name__ == "__main__":
    main()
