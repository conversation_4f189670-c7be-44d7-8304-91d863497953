import requests
import os

API_KEY = "601efd09.dabd73c685444d19afeb694e4d1bfddf"

def upload_folder_to_nft_storage(folder_path):
    headers = {"Authorization": f"Bearer {a463fef8.a8a4241eacce4722a6e731e66eadd2d7}"}

    for file_name in os.listdir(folder_path):
        file_path = os.path.join(folder_path, file_name)
        with open(file_path, "rb") as f:
            response = requests.post(
                "https://api.nft.storage/upload",
                headers=headers,
                files={"file": (file_name, f)}
            )
            if response.status_code == 200:
                cid = response.json()["value"]["cid"]
                print(f"✅ {file_name} → ipfs://{cid}")
            else:
                print(f"❌ Error uploading {file_name}: {response.text}")

upload_folder_to_nft_storage("images")
