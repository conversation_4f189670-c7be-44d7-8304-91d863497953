#!/usr/bin/env python3
"""
Create animated smoke effect images for NFT generation
"""

from PIL import Image, ImageDraw, ImageFilter
import os
import random
import math
import numpy as np

def create_animated_smoke(size=(1000, 1000), frames=20):
    """Create realistic animated smoke effect"""
    frame_list = []
    
    # Generate smoke particles that persist across frames
    smoke_particles = []
    for _ in range(80):
        smoke_particles.append({
            'start_x': random.randint(300, 700),
            'start_y': random.randint(600, 800),
            'velocity_x': random.uniform(-1, 1),
            'velocity_y': random.uniform(-3, -1),
            'size': random.randint(15, 40),
            'life': random.randint(10, frames),
            'opacity_start': random.randint(80, 150),
            'swirl_factor': random.uniform(0.1, 0.3)
        })
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        for particle in smoke_particles:
            # Calculate particle age
            age = frame
            if age >= particle['life']:
                continue
            
            # Animated movement with wind and swirl
            wind_effect = math.sin(frame * 0.1) * 20
            swirl_x = math.sin(frame * particle['swirl_factor']) * 30
            swirl_y = math.cos(frame * particle['swirl_factor'] * 0.7) * 15
            
            x = particle['start_x'] + particle['velocity_x'] * age + wind_effect + swirl_x
            y = particle['start_y'] + particle['velocity_y'] * age + swirl_y
            
            # Smoke dissipation - particles get bigger and more transparent
            age_factor = age / particle['life']
            current_size = particle['size'] + int(age_factor * 20)
            current_opacity = int(particle['opacity_start'] * (1 - age_factor))
            
            if current_opacity <= 0:
                continue
            
            # Smoke color - gray with slight variations
            gray_value = random.randint(100, 180)
            smoke_color = (gray_value, gray_value, gray_value, current_opacity)
            
            # Ensure valid coordinates
            x1, y1 = max(0, x - current_size), max(0, y - current_size)
            x2, y2 = min(size[0], x + current_size), min(size[1], y + current_size)
            
            if x2 > x1 and y2 > y1:
                draw.ellipse([x1, y1, x2, y2], fill=smoke_color)
        
        # Apply heavy blur for realistic smoke effect
        img = img.filter(ImageFilter.GaussianBlur(radius=8))
        frame_list.append(img)
    
    return frame_list

def create_animated_toxic_smoke(size=(1000, 1000), frames=16):
    """Create animated toxic green smoke effect"""
    frame_list = []
    
    # Generate toxic smoke particles
    toxic_particles = []
    for _ in range(60):
        toxic_particles.append({
            'start_x': random.randint(200, 800),
            'start_y': random.randint(500, 900),
            'velocity_x': random.uniform(-0.8, 0.8),
            'velocity_y': random.uniform(-2.5, -0.5),
            'size': random.randint(12, 35),
            'life': random.randint(8, frames),
            'opacity_start': random.randint(60, 120),
            'toxicity': random.uniform(0.3, 1.0)
        })
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        for particle in toxic_particles:
            age = frame
            if age >= particle['life']:
                continue
            
            # Toxic smoke movement - more chaotic
            chaos_x = math.sin(frame * 0.2 + particle['toxicity']) * 25
            chaos_y = math.cos(frame * 0.15 + particle['toxicity']) * 10
            
            x = particle['start_x'] + particle['velocity_x'] * age + chaos_x
            y = particle['start_y'] + particle['velocity_y'] * age + chaos_y
            
            # Toxic dissipation
            age_factor = age / particle['life']
            current_size = particle['size'] + int(age_factor * 15)
            current_opacity = int(particle['opacity_start'] * (1 - age_factor))
            
            if current_opacity <= 0:
                continue
            
            # Toxic green color with variations
            green_intensity = int(150 + particle['toxicity'] * 105)
            red_tint = random.randint(20, 60)
            toxic_color = (red_tint, green_intensity, 50, current_opacity)
            
            # Ensure valid coordinates
            x1, y1 = max(0, x - current_size), max(0, y - current_size)
            x2, y2 = min(size[0], x + current_size), min(size[1], y + current_size)
            
            if x2 > x1 and y2 > y1:
                draw.ellipse([x1, y1, x2, y2], fill=toxic_color)
        
        img = img.filter(ImageFilter.GaussianBlur(radius=6))
        frame_list.append(img)
    
    return frame_list

def create_animated_steam(size=(1000, 1000), frames=18):
    """Create animated steam/vapor effect"""
    frame_list = []
    
    # Generate steam particles
    steam_particles = []
    for _ in range(50):
        steam_particles.append({
            'start_x': random.randint(250, 750),
            'start_y': random.randint(700, 950),
            'velocity_x': random.uniform(-0.5, 0.5),
            'velocity_y': random.uniform(-4, -2),
            'size': random.randint(8, 25),
            'life': random.randint(12, frames),
            'opacity_start': random.randint(100, 180),
            'heat_factor': random.uniform(0.2, 0.8)
        })
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        for particle in steam_particles:
            age = frame
            if age >= particle['life']:
                continue
            
            # Steam rises with heat distortion
            heat_wave = math.sin(frame * 0.3 + particle['heat_factor']) * 15
            thermal_x = math.cos(frame * 0.2 + particle['heat_factor']) * 20
            
            x = particle['start_x'] + particle['velocity_x'] * age + thermal_x
            y = particle['start_y'] + particle['velocity_y'] * age + heat_wave
            
            # Steam dissipation - faster than smoke
            age_factor = age / particle['life']
            current_size = particle['size'] + int(age_factor * 25)
            current_opacity = int(particle['opacity_start'] * (1 - age_factor * 1.2))
            
            if current_opacity <= 0:
                continue
            
            # Steam color - white/light gray with heat tint
            heat_tint = int(particle['heat_factor'] * 50)
            steam_color = (255 - heat_tint, 255 - heat_tint//2, 255, current_opacity)
            
            # Ensure valid coordinates
            x1, y1 = max(0, x - current_size), max(0, y - current_size)
            x2, y2 = min(size[0], x + current_size), min(size[1], y + current_size)
            
            if x2 > x1 and y2 > y1:
                draw.ellipse([x1, y1, x2, y2], fill=steam_color)
        
        img = img.filter(ImageFilter.GaussianBlur(radius=5))
        frame_list.append(img)
    
    return frame_list

def create_animated_dark_smoke(size=(1000, 1000), frames=22):
    """Create animated dark/black smoke effect"""
    frame_list = []
    
    # Generate dark smoke particles
    dark_particles = []
    for _ in range(70):
        dark_particles.append({
            'start_x': random.randint(300, 700),
            'start_y': random.randint(600, 900),
            'velocity_x': random.uniform(-1.2, 1.2),
            'velocity_y': random.uniform(-2.8, -1.2),
            'size': random.randint(18, 45),
            'life': random.randint(15, frames),
            'opacity_start': random.randint(90, 160),
            'darkness': random.uniform(0.4, 1.0)
        })
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        for particle in dark_particles:
            age = frame
            if age >= particle['life']:
                continue
            
            # Dark smoke movement - heavy and slow
            heavy_drift = math.sin(frame * 0.08 + particle['darkness']) * 35
            gravity_effect = age * 0.1  # Slight downward pull
            
            x = particle['start_x'] + particle['velocity_x'] * age + heavy_drift
            y = particle['start_y'] + particle['velocity_y'] * age + gravity_effect
            
            # Dark smoke dissipation
            age_factor = age / particle['life']
            current_size = particle['size'] + int(age_factor * 30)
            current_opacity = int(particle['opacity_start'] * (1 - age_factor * 0.8))
            
            if current_opacity <= 0:
                continue
            
            # Dark smoke color - very dark gray/black
            darkness_value = int(30 + (1 - particle['darkness']) * 50)
            dark_color = (darkness_value, darkness_value, darkness_value, current_opacity)
            
            # Ensure valid coordinates
            x1, y1 = max(0, x - current_size), max(0, y - current_size)
            x2, y2 = min(size[0], x + current_size), min(size[1], y + current_size)
            
            if x2 > x1 and y2 > y1:
                draw.ellipse([x1, y1, x2, y2], fill=dark_color)
        
        img = img.filter(ImageFilter.GaussianBlur(radius=10))
        frame_list.append(img)
    
    return frame_list

def create_animated_magical_mist(size=(1000, 1000), frames=24):
    """Create animated magical mist effect"""
    frame_list = []
    
    # Generate magical mist particles
    mist_particles = []
    for _ in range(90):
        mist_particles.append({
            'start_x': random.randint(100, 900),
            'start_y': random.randint(400, 800),
            'velocity_x': random.uniform(-0.8, 0.8),
            'velocity_y': random.uniform(-1.5, 1.5),
            'size': random.randint(10, 30),
            'life': random.randint(18, frames),
            'opacity_start': random.randint(40, 100),
            'magic_phase': random.uniform(0, 2 * math.pi),
            'color_shift': random.randint(0, 360)
        })
    
    for frame in range(frames):
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        for particle in mist_particles:
            age = frame
            if age >= particle['life']:
                continue
            
            # Magical movement - swirling and floating
            magic_swirl_x = math.sin(frame * 0.1 + particle['magic_phase']) * 40
            magic_swirl_y = math.cos(frame * 0.08 + particle['magic_phase']) * 30
            float_effect = math.sin(frame * 0.05 + particle['magic_phase']) * 10
            
            x = particle['start_x'] + particle['velocity_x'] * age + magic_swirl_x
            y = particle['start_y'] + particle['velocity_y'] * age + magic_swirl_y + float_effect
            
            # Magical mist dissipation
            age_factor = age / particle['life']
            current_size = particle['size'] + int(age_factor * 20)
            current_opacity = int(particle['opacity_start'] * (1 - age_factor))
            
            if current_opacity <= 0:
                continue
            
            # Magical color shifting
            color_cycle = (particle['color_shift'] + frame * 5) % 360
            if color_cycle < 60:
                mist_color = (200, 150, 255, current_opacity)  # Purple
            elif color_cycle < 120:
                mist_color = (150, 200, 255, current_opacity)  # Blue
            elif color_cycle < 180:
                mist_color = (150, 255, 200, current_opacity)  # Cyan
            elif color_cycle < 240:
                mist_color = (200, 255, 150, current_opacity)  # Green
            elif color_cycle < 300:
                mist_color = (255, 200, 150, current_opacity)  # Orange
            else:
                mist_color = (255, 150, 200, current_opacity)  # Pink
            
            # Ensure valid coordinates
            x1, y1 = max(0, x - current_size), max(0, y - current_size)
            x2, y2 = min(size[0], x + current_size), min(size[1], y + current_size)
            
            if x2 > x1 and y2 > y1:
                draw.ellipse([x1, y1, x2, y2], fill=mist_color)
        
        img = img.filter(ImageFilter.GaussianBlur(radius=7))
        frame_list.append(img)
    
    return frame_list

def save_animated_gif(frames, filename, duration=100):
    """Save frames as animated GIF"""
    if frames:
        frames[0].save(
            filename,
            save_all=True,
            append_images=frames[1:],
            duration=duration,
            loop=0,
            format='GIF'
        )

def main():
    """Generate animated smoke effects"""
    energy_dir = "energy_animated"
    os.makedirs(energy_dir, exist_ok=True)
    
    print("Creating animated smoke effects...")
    
    # Create smoke effects
    smoke_effects = [
        ("Animated_Smoke", create_animated_smoke, 120),
        ("Animated_Toxic_Smoke", create_animated_toxic_smoke, 140),
        ("Animated_Steam", create_animated_steam, 100),
        ("Animated_Dark_Smoke", create_animated_dark_smoke, 150),
        ("Animated_Magical_Mist", create_animated_magical_mist, 80),
    ]
    
    for effect_name, effect_func, duration in smoke_effects:
        print(f"Creating {effect_name}...")
        frames = effect_func()
        
        # Save as GIF
        gif_path = os.path.join(energy_dir, f"{effect_name}.gif")
        save_animated_gif(frames, gif_path, duration)
        print(f"Saved: {gif_path}")
        
        # Also save first frame as PNG for static version
        png_path = os.path.join(energy_dir, f"{effect_name}.png")
        frames[0].save(png_path, 'PNG')
        print(f"Saved static: {png_path}")
    
    print(f"\nCreated {len(smoke_effects)} animated smoke effects!")
    print("Smoke effects are ready!")

if __name__ == "__main__":
    main()
