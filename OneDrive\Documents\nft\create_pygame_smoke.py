#!/usr/bin/env python3
"""
Create Pygame-style smoke effects for NFT generation
Based on the provided Pygame smoke animation code
"""

from PIL import Image, ImageDraw, ImageFilter, ImageEnhance
import os
import random
import math

def create_pygame_style_smoke(size=(1080, 1080), frames=120):
    """Create Pygame-style smoke animation"""
    frame_list = []
    
    # Background color (matching Pygame version)
    background_color = (13, 17, 23, 255)  # #0d1117
    
    # Particle system
    particles = []
    particle_count = 40
    
    # Initialize particles
    for _ in range(particle_count):
        particles.append({
            'x': random.randint(0, size[0]),
            'y': size[1] + random.randint(0, 100),
            'size': random.randint(80, 120),
            'speed_x': (random.random() * 2 - 1) * 0.5,
            'speed_y': -(random.random() * 1.5 + 0.5),
            'alpha': random.randint(25, 100),
            'angle': random.randint(0, 360),
            'spin': (random.random() - 0.5) * 0.1,
            'life': 0
        })
    
    for frame in range(frames):
        # Create background
        img = Image.new('RGBA', size, background_color)
        draw = ImageDraw.Draw(img)
        
        # Update and draw particles
        for particle in particles:
            # Update particle position
            particle['x'] += particle['speed_x']
            particle['y'] += particle['speed_y']
            particle['angle'] += particle['spin']
            particle['life'] += 1
            
            # Reset particle if it goes off screen
            if particle['y'] < -particle['size']:
                particle['y'] = size[1] + particle['size']
                particle['x'] = random.randint(0, size[0])
                particle['life'] = 0
            
            # Draw particle with rotation effect
            current_alpha = max(0, min(255, particle['alpha']))
            if current_alpha > 0:
                # Create smoke layers for realistic effect
                for layer in range(4):
                    layer_size = particle['size'] * (1 - layer * 0.15)
                    layer_alpha = current_alpha // (layer + 1)
                    
                    if layer_size > 0 and layer_alpha > 0:
                        # Smoke color - light gray with transparency
                        smoke_color = (200 - layer * 20, 200 - layer * 20, 200 - layer * 20, layer_alpha)
                        
                        # Apply rotation (simplified)
                        angle_rad = math.radians(particle['angle'])
                        rotation_offset_x = math.cos(angle_rad) * layer * 2
                        rotation_offset_y = math.sin(angle_rad) * layer * 2
                        
                        x1 = max(0, particle['x'] - layer_size + rotation_offset_x)
                        y1 = max(0, particle['y'] - layer_size + rotation_offset_y)
                        x2 = min(size[0], particle['x'] + layer_size + rotation_offset_x)
                        y2 = min(size[1], particle['y'] + layer_size + rotation_offset_y)
                        
                        if x2 > x1 and y2 > y1:
                            draw.ellipse([x1, y1, x2, y2], fill=smoke_color)
        
        # Apply blur for realistic smoke effect
        img = img.filter(ImageFilter.GaussianBlur(radius=4))
        frame_list.append(img)
    
    return frame_list

def create_rising_smoke_columns(size=(1080, 1080), frames=100):
    """Create multiple rising smoke columns"""
    frame_list = []
    
    background_color = (13, 17, 23, 255)
    
    # Multiple smoke sources
    smoke_sources = [
        {'x': size[0] * 0.2, 'y': size[1] * 0.9},
        {'x': size[0] * 0.5, 'y': size[1] * 0.85},
        {'x': size[0] * 0.8, 'y': size[1] * 0.9},
    ]
    
    particles = []
    
    for frame in range(frames):
        img = Image.new('RGBA', size, background_color)
        draw = ImageDraw.Draw(img)
        
        # Add new particles from each source
        for source in smoke_sources:
            if random.random() < 0.7:  # 70% chance to emit
                particles.append({
                    'x': source['x'] + random.randint(-20, 20),
                    'y': source['y'] + random.randint(-10, 10),
                    'size': random.randint(60, 100),
                    'speed_x': (random.random() - 0.5) * 0.8,
                    'speed_y': -random.uniform(1, 2.5),
                    'alpha': random.randint(40, 120),
                    'angle': random.randint(0, 360),
                    'spin': random.uniform(-0.2, 0.2),
                    'life': 0,
                    'max_life': random.randint(80, 120)
                })
        
        # Update and draw particles
        for i in range(len(particles) - 1, -1, -1):
            particle = particles[i]
            particle['life'] += 1
            
            # Update position
            particle['x'] += particle['speed_x']
            particle['y'] += particle['speed_y']
            particle['angle'] += particle['spin']
            
            # Fade out over time
            age_factor = particle['life'] / particle['max_life']
            current_alpha = int(particle['alpha'] * (1 - age_factor))
            current_size = particle['size'] * (1 + age_factor * 0.5)
            
            # Remove dead particles
            if particle['life'] > particle['max_life'] or current_alpha <= 0:
                particles.pop(i)
                continue
            
            # Draw particle
            if current_alpha > 0:
                for layer in range(3):
                    layer_size = current_size * (1 - layer * 0.2)
                    layer_alpha = current_alpha // (layer + 1)
                    
                    if layer_size > 0 and layer_alpha > 0:
                        smoke_color = (180 - layer * 15, 180 - layer * 15, 180 - layer * 15, layer_alpha)
                        
                        angle_rad = math.radians(particle['angle'])
                        offset_x = math.cos(angle_rad) * layer * 3
                        offset_y = math.sin(angle_rad) * layer * 3
                        
                        x1 = max(0, particle['x'] - layer_size + offset_x)
                        y1 = max(0, particle['y'] - layer_size + offset_y)
                        x2 = min(size[0], particle['x'] + layer_size + offset_x)
                        y2 = min(size[1], particle['y'] + layer_size + offset_y)
                        
                        if x2 > x1 and y2 > y1:
                            draw.ellipse([x1, y1, x2, y2], fill=smoke_color)
        
        img = img.filter(ImageFilter.GaussianBlur(radius=5))
        frame_list.append(img)
    
    return frame_list

def create_swirling_pygame_smoke(size=(1080, 1080), frames=150):
    """Create swirling Pygame-style smoke"""
    frame_list = []
    
    background_color = (13, 17, 23, 255)
    center_x, center_y = size[0] // 2, size[1] // 2
    
    particles = []
    max_particles = 60
    
    for frame in range(frames):
        img = Image.new('RGBA', size, background_color)
        draw = ImageDraw.Draw(img)
        
        # Swirling motion parameters
        swirl_angle = frame * 0.05
        swirl_radius = 100 + math.sin(frame * 0.02) * 50
        
        # Add new particles in swirling pattern
        if len(particles) < max_particles:
            for _ in range(2):
                angle = swirl_angle + random.uniform(-0.5, 0.5)
                radius = swirl_radius + random.uniform(-30, 30)
                
                start_x = center_x + math.cos(angle) * radius
                start_y = center_y + math.sin(angle) * radius
                
                particles.append({
                    'x': start_x,
                    'y': start_y,
                    'size': random.randint(70, 110),
                    'speed_x': math.cos(angle + math.pi/2) * 0.3 + random.uniform(-0.2, 0.2),
                    'speed_y': math.sin(angle + math.pi/2) * 0.3 - random.uniform(0.5, 1.0),
                    'alpha': random.randint(30, 100),
                    'angle': random.randint(0, 360),
                    'spin': random.uniform(-0.3, 0.3),
                    'life': 0,
                    'max_life': random.randint(100, 150),
                    'swirl_factor': random.uniform(0.01, 0.03)
                })
        
        # Update and draw particles
        for i in range(len(particles) - 1, -1, -1):
            particle = particles[i]
            particle['life'] += 1
            
            # Swirling motion
            swirl_force_x = math.cos(particle['life'] * particle['swirl_factor']) * 0.2
            swirl_force_y = math.sin(particle['life'] * particle['swirl_factor']) * 0.2
            
            particle['x'] += particle['speed_x'] + swirl_force_x
            particle['y'] += particle['speed_y'] + swirl_force_y
            particle['angle'] += particle['spin']
            
            # Aging effects
            age_factor = particle['life'] / particle['max_life']
            current_alpha = int(particle['alpha'] * (1 - age_factor))
            current_size = particle['size'] * (1 + age_factor * 0.3)
            
            # Remove dead particles
            if particle['life'] > particle['max_life'] or current_alpha <= 0:
                particles.pop(i)
                continue
            
            # Draw swirling particle
            if current_alpha > 0:
                for layer in range(4):
                    layer_size = current_size * (1 - layer * 0.18)
                    layer_alpha = current_alpha // (layer + 1)
                    
                    if layer_size > 0 and layer_alpha > 0:
                        smoke_color = (190 - layer * 18, 190 - layer * 18, 190 - layer * 18, layer_alpha)
                        
                        # Enhanced rotation for swirling effect
                        angle_rad = math.radians(particle['angle'])
                        rotation_x = math.cos(angle_rad) * layer * 4
                        rotation_y = math.sin(angle_rad) * layer * 4
                        
                        x1 = max(0, particle['x'] - layer_size + rotation_x)
                        y1 = max(0, particle['y'] - layer_size + rotation_y)
                        x2 = min(size[0], particle['x'] + layer_size + rotation_x)
                        y2 = min(size[1], particle['y'] + layer_size + rotation_y)
                        
                        if x2 > x1 and y2 > y1:
                            draw.ellipse([x1, y1, x2, y2], fill=smoke_color)
        
        img = img.filter(ImageFilter.GaussianBlur(radius=6))
        frame_list.append(img)
    
    return frame_list

def save_animated_gif(frames, filename, duration=100):
    """Save frames as animated GIF"""
    if frames:
        frames[0].save(
            filename,
            save_all=True,
            append_images=frames[1:],
            duration=duration,
            loop=0,
            format='GIF'
        )

def main():
    """Generate Pygame-style smoke effects"""
    energy_dir = "energy_animated"
    os.makedirs(energy_dir, exist_ok=True)
    
    print("Creating Pygame-style smoke effects...")
    
    # Create Pygame-style smoke effects
    pygame_effects = [
        ("Pygame_Style_Smoke", create_pygame_style_smoke, 100),
        ("Pygame_Rising_Columns", create_rising_smoke_columns, 120),
        ("Pygame_Swirling_Smoke", create_swirling_pygame_smoke, 80),
    ]
    
    for effect_name, effect_func, duration in pygame_effects:
        print(f"Creating {effect_name}...")
        frames = effect_func()
        
        # Save as GIF
        gif_path = os.path.join(energy_dir, f"{effect_name}.gif")
        save_animated_gif(frames, gif_path, duration)
        print(f"Saved: {gif_path}")
        
        # Also save first frame as PNG for static version
        png_path = os.path.join(energy_dir, f"{effect_name}.png")
        frames[0].save(png_path, 'PNG')
        print(f"Saved static: {png_path}")
    
    print(f"\nCreated {len(pygame_effects)} Pygame-style smoke effects!")
    print("Pygame-style smoke effects are ready!")

if __name__ == "__main__":
    main()
